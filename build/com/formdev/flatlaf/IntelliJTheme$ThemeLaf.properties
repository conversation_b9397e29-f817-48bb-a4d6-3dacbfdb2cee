#
# Copyright 2019 FormDev Software GmbH
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

#
# This file is loaded for all IntelliJ Platform themes.
#
# Documentation:
#  - https://www.formdev.com/flatlaf/properties-files/
#  - https://www.formdev.com/flatlaf/how-to-customize/
#
# Properties in this file are applied in following order:
#   1. properties without '{...}' and without '[...]' prefix
#   2. properties specified in .theme.json file
#   3. properties starting with '{*}'
#   4. properties starting with '{*-light}' or '{*-dark}'
#   5. properties starting with '{author-<author>}',
#      where '<author>' is replaced with "author" value from .theme.json file 
#   6. properties starting with '{<name>---<author>}',
#      where '<name>' and '<author>' are replaced with "name" and "author" values from .theme.json file 
#   7. properties starting with '{<name>}',
#      where '<name>' is replaced with "name" value from .theme.json file 
#   8. properties with '[...]' prefix
#


#---- system colors ----

# fix (most) system colors because they are usually not set in .json files
desktop = $TextField.background
activeCaptionText = $TextField.foreground
inactiveCaptionText = $TextField.foreground
window = $Panel.background
windowBorder = $TextField.foreground
windowText = $TextField.foreground
menu = $Menu.background
menuText = $Menu.foreground
text = $TextField.background
textText = $TextField.foreground
textHighlight = $TextField.selectionBackground
textHighlightText = $TextField.selectionForeground
textInactiveText = $TextField.inactiveForeground
control = $Panel.background
controlText = $TextField.foreground
info = $ToolTip.background
infoText = $ToolTip.foreground


#---- variables ----

# make sure that accent color (set via FlatLaf.setSystemColorGetter()) is ignored 
@accentColor = null

# use same accent color for checkmark, slider, tab underline, etc.
@accentBase2Color = @accentBaseColor

# use fixed color because it is used in borders
@cellFocusColor = #222


#---- CheckBoxMenuItem ----

# colors from intellij/checkmark.svg and darcula/checkmark.svg
[light]CheckBoxMenuItem.icon.checkmarkColor=#3E3E3C
[dark]CheckBoxMenuItem.icon.checkmarkColor=#fff9


#---- Slider ----

# this "reverses" definition in FlatLightLaf/FlatDarkLaf.properties 
Slider.trackValueColor = $Slider.thumbColor
Slider.thumbColor = @accentSliderColor


#---- Spinner ----

# Spinner arrow button always has same colors as ComboBox arrow button
Spinner.buttonBackground = $ComboBox.buttonEditableBackground
Spinner.buttonArrowColor = $ComboBox.buttonArrowColor
Spinner.buttonDisabledArrowColor = $ComboBox.buttonDisabledArrowColor


#---- TabbedPane ----

# colors from JBUI.CurrentTheme.DefaultTabs.inactiveUnderlineColor()
{*-light}TabbedPane.inactiveUnderlineColor = #9ca7b8
{*-dark}TabbedPane.inactiveUnderlineColor = #747a80


#---- ToggleButton ----

{*}ToggleButton.background = $Button.background
{*-dark}ToggleButton.selectedBackground = lighten($ToggleButton.background,15%,derived)
{*-dark}ToggleButton.disabledSelectedBackground = lighten($ToggleButton.background,5%,derived)


#---- theme specific ----

@ijMenuCheckBackgroundL10 = lighten(@selectionBackground,10%,derived noAutoInverse)
@ijMenuCheckBackgroundL20 = lighten(@selectionBackground,20%,derived noAutoInverse)
@ijMenuCheckBackgroundD10 = darken(@selectionBackground,10%,derived noAutoInverse)

@ijSeparatorLight = shade(@background,15%)
@ijSeparatorDark = tint(@background,25%)

@ijTextBackgroundL3 = lighten($Panel.background,3%)
@ijTextBackgroundL4 = lighten($Panel.background,4%)

{Arc_Theme}@selectionInactiveForeground = @selectionForeground
{Arc_Theme}CheckBoxMenuItem.foreground = $MenuItem.foreground
{Arc_Theme}PopupMenu.foreground = $MenuItem.foreground
{Arc_Theme}RadioButtonMenuItem.foreground = $MenuItem.foreground

{Arc_Theme_-_Orange}@selectionInactiveForeground = @selectionForeground
{Arc_Theme_-_Orange}CheckBoxMenuItem.foreground = $MenuItem.foreground
{Arc_Theme_-_Orange}PopupMenu.foreground = $MenuItem.foreground
{Arc_Theme_-_Orange}RadioButtonMenuItem.foreground = $MenuItem.foreground

{Arc_Theme_Dark}CheckBoxMenuItem.foreground = $MenuItem.foreground
{Arc_Theme_Dark}PopupMenu.foreground = $MenuItem.foreground
{Arc_Theme_Dark}RadioButtonMenuItem.foreground = $MenuItem.foreground
{Arc_Theme_Dark}ToolBar.background = @background

{Arc_Theme_Dark_-_Orange}CheckBoxMenuItem.foreground = $MenuItem.foreground
{Arc_Theme_Dark_-_Orange}PopupMenu.foreground = $MenuItem.foreground
{Arc_Theme_Dark_-_Orange}ProgressBar.selectionForeground = #fff
{Arc_Theme_Dark_-_Orange}RadioButtonMenuItem.foreground = $MenuItem.foreground
{Arc_Theme_Dark_-_Orange}ToolBar.background = @background

{Carbon}Separator.foreground = @ijSeparatorDark
{Carbon}ToolBar.separatorColor = $Separator.foreground
{Carbon}Table.selectionBackground = $List.selectionBackground
{Carbon}TextField.background = @ijTextBackgroundL4

{Cobalt_2}@accentBaseColor = $ColorPalette.hue3
{Cobalt_2}CheckBox.icon.background = @background
{Cobalt_2}MenuItem.checkBackground = @ijMenuCheckBackgroundL10
{Cobalt_2}MenuItem.underlineSelectionCheckBackground = @ijMenuCheckBackgroundL10
{Cobalt_2}ComboBox.background = @ijTextBackgroundL3
{Cobalt_2}Slider.thumbColor = $ProgressBar.foreground
{Cobalt_2}Slider.disabledTrackColor = $Separator.foreground
{Cobalt_2}TextField.background = @ijTextBackgroundL3
{Cobalt_2}Table.background = $List.background
{Cobalt_2}Tree.background = $List.background

{Cyan_light}@disabledForeground = tint(@foreground,30%)
{Cyan_light}*.disabledText = @disabledForeground
{Cyan_light}*.disabledForeground = @disabledForeground
{Cyan_light}*.inactiveForeground = @disabledForeground
{Cyan_light}Button.background = @buttonBackground
{Cyan_light}MenuItem.checkBackground = @ijMenuCheckBackgroundL20
{Cyan_light}MenuItem.underlineSelectionCheckBackground = @ijMenuCheckBackgroundL20

{Dark_Flat_Theme}@accentBaseColor = $TabbedPane.underlineColor
{Dark_Flat_Theme}@disabledForeground = #808080
{Dark_Flat_Theme}*.disabledText = @disabledForeground
{Dark_Flat_Theme}*.disabledForeground = @disabledForeground
{Dark_Flat_Theme}*.inactiveForeground = @disabledForeground
{Dark_Flat_Theme}TableHeader.background = #3B3B3B
{Dark_Flat_Theme}CheckBoxMenuItem.selectionForeground = $MenuItem.selectionForeground
{Dark_Flat_Theme}ComboBox.background = $TextField.background
{Dark_Flat_Theme}ComboBox.buttonBackground = $ComboBox.background
{Dark_Flat_Theme}List.selectionForeground = $Tree.selectionForeground
{Dark_Flat_Theme}ProgressBar.selectionForeground = @foreground
{Dark_Flat_Theme}RadioButtonMenuItem.selectionForeground = $MenuItem.selectionForeground
{Dark_Flat_Theme}Separator.foreground = @ijSeparatorDark
{Dark_Flat_Theme}Slider.trackColor = $ProgressBar.background
{Dark_Flat_Theme}Slider.thumbColor = fade($ProgressBar.foreground,100%)
{Dark_Flat_Theme}TextPane.foreground = $TextField.foreground
{Dark_Flat_Theme}ToggleButton.foreground = $Button.foreground

{Dracula---Zihan_Ma}CheckBox.icon.background = @background
{Dracula---Zihan_Ma}ComboBox.selectionBackground = $List.selectionBackground
{Dracula---Zihan_Ma}ProgressBar.selectionBackground = #fff
{Dracula---Zihan_Ma}ProgressBar.selectionForeground = #fff
{Dracula---Zihan_Ma}Slider.trackColor = $?ColorPalette.selectionBackground
{Dracula---Zihan_Ma}ToggleButton.foreground = $Button.foreground

{Gradianto_Dark_Fuchsia}MenuItem.checkBackground = @ijMenuCheckBackgroundL10
{Gradianto_Dark_Fuchsia}MenuItem.underlineSelectionCheckBackground = @ijMenuCheckBackgroundL10
{Gradianto_Dark_Fuchsia}TextField.background = @ijTextBackgroundL4
{Gradianto_Dark_Fuchsia}Tree.background = $List.background
{Gradianto_Dark_Fuchsia}Tree.selectionInactiveBackground = $List.selectionInactiveBackground
{Gradianto_Dark_Fuchsia}Separator.foreground = @ijSeparatorDark
{Gradianto_Dark_Fuchsia}ToolBar.separatorColor = $Separator.foreground
{Gradianto_Dark_Fuchsia}ProgressBar.background = $ScrollBar.track
{Gradianto_Dark_Fuchsia}Slider.trackColor = $ScrollBar.track

{Gradianto_Deep_Ocean}Separator.foreground = @ijSeparatorDark
{Gradianto_Deep_Ocean}ToolBar.separatorColor = $Separator.foreground
{Gradianto_Deep_Ocean}TextField.background = @ijTextBackgroundL3
{Gradianto_Deep_Ocean}Tree.background = $List.background

{Gradianto_Midnight_Blue}ScrollBar.thumb = #533B6B
{Gradianto_Midnight_Blue}Separator.foreground = @ijSeparatorDark
{Gradianto_Midnight_Blue}ToolBar.separatorColor = $Separator.foreground
{Gradianto_Midnight_Blue}Table.selectionForeground = $List.selectionForeground
{Gradianto_Midnight_Blue}TextField.background = @ijTextBackgroundL4
{Gradianto_Midnight_Blue}Tree.background = $List.background

{Gradianto_Nature_Green}Separator.foreground = @ijSeparatorDark
{Gradianto_Nature_Green}ToolBar.separatorColor = $Separator.foreground
{Gradianto_Nature_Green}Table.selectionForeground = $List.selectionForeground
{Gradianto_Nature_Green}TextField.background = @ijTextBackgroundL4

{Gray}@disabledForeground = tint(@foreground,40%)
{Gray}*.disabledText = @disabledForeground
{Gray}*.disabledForeground = @disabledForeground
{Gray}*.inactiveForeground = @disabledForeground
{Gray}Button.background = @buttonBackground
{Gray}Separator.foreground = @ijSeparatorLight
{Gray}ToolBar.separatorColor = $Separator.foreground

{Gruvbox_Dark_Hard}@accentBaseColor = #4B6EAF
{Gruvbox_Dark_Hard}ComboBox.background = @ijTextBackgroundL3
{Gruvbox_Dark_Hard}ComboBox.buttonBackground = $ComboBox.background
{Gruvbox_Dark_Hard}TextField.background = @ijTextBackgroundL3

{Hiberbee_Dark}@disabledForeground = $ColorPalette.light3
{Hiberbee_Dark}*.disabledText = @disabledForeground
{Hiberbee_Dark}*.disabledForeground = @disabledForeground
{Hiberbee_Dark}*.inactiveForeground = @disabledForeground
{Hiberbee_Dark}List.selectionInactiveBackground = $Table.selectionInactiveBackground
{Hiberbee_Dark}ProgressBar.background = $Separator.foreground
{Hiberbee_Dark}RadioButtonMenuItem.selectionForeground = $MenuItem.selectionForeground
{Hiberbee_Dark}Slider.trackColor = $ColorPalette.light1
{Hiberbee_Dark}Slider.trackColor = $ColorPalette.dark10
{Hiberbee_Dark}Slider.thumbColor = @accentBaseColor
{Hiberbee_Dark}ToggleButton.foreground = $Button.foreground
{Hiberbee_Dark}ToolBar.background = @background

{High_Contrast}@accentBaseColor = $TabbedPane.underlineColor
{High_Contrast}Slider.thumbBorderColor = $Slider.thumbColor
{High_Contrast}Slider.focusedThumbBorderColor = @background
{High_Contrast}Slider.focusedColor = $Component.focusColor
{High_Contrast}Slider.focusWidth = 2
{High_Contrast}ToggleButton.selectedBackground = @selectionBackground
{High_Contrast}ToggleButton.selectedForeground = @selectionForeground
{High_Contrast}ToggleButton.disabledSelectedBackground = shade(@selectionBackground,50%)
{High_Contrast}ToggleButton.toolbar.selectedBackground = @selectionBackground
{High_Contrast}[style]Button.inTextField = \
	toolbar.hoverBackground: #444; \
	toolbar.pressedBackground: #666; \
	toolbar.selectedBackground: @selectionBackground

{Light_Flat}@accentBaseColor = $TabbedPane.underlineColor
{Light_Flat}@accentFocusColor = lighten(@accentBaseColor,15%)
{Light_Flat}@disabledForeground = #808080
{Light_Flat}*.disabledText = @disabledForeground
{Light_Flat}*.disabledForeground = @disabledForeground
{Light_Flat}*.inactiveForeground = @disabledForeground
{Light_Flat}CheckBox.icon[filled].background = #fff
{Light_Flat}CheckBox.icon[filled].checkmarkColor = #fff
{Light_Flat}ComboBox.background = $ComboBox.editableBackground
{Light_Flat}ComboBox.buttonBackground = $ComboBox.background
{Light_Flat}ProgressBar.selectionForeground = @foreground
{Light_Flat}Separator.foreground = @ijSeparatorLight
{Light_Flat}TableHeader.background = #E5E5E9
{Light_Flat}TextPane.foreground = $TextField.foreground
{Light_Flat}ToggleButton.foreground = $Button.foreground
{Light_Flat}CheckBoxMenuItem.selectionForeground = $MenuItem.selectionForeground
{Light_Flat}RadioButtonMenuItem.selectionForeground = $MenuItem.selectionForeground

{Monocai}@accentUnderlineColor = @accentBaseColor
{Monocai}*.acceleratorForeground = @menuAcceleratorForeground
{Monocai}*.acceleratorSelectionForeground = @menuAcceleratorSelectionForeground
{Monocai}Button.default.foreground = @background
{Monocai}MenuItem.checkBackground = @ijMenuCheckBackgroundL10
{Monocai}TabbedPane.underlineColor = @accentUnderlineColor
{Monocai}TextField.background = @ijTextBackgroundL4
@Monocai.selectionBackground = $TextField.selectionBackground
{Monocai}ComboBox.selectionBackground = @Monocai.selectionBackground
{Monocai}List.selectionBackground = @Monocai.selectionBackground
{Monocai}Table.selectionBackground = @Monocai.selectionBackground
{Monocai}Tree.selectionBackground = @Monocai.selectionBackground
@Monocai.selectionInactiveBackground = $MenuItem.selectionBackground
{Monocai}List.selectionInactiveBackground = @Monocai.selectionInactiveBackground
{Monocai}Table.selectionInactiveBackground = @Monocai.selectionInactiveBackground
{Monocai}Tree.selectionInactiveBackground = @Monocai.selectionInactiveBackground

{Monokai_Pro---Subtheme}@disabledForeground = shade(@foreground,40%)
{Monokai_Pro---Subtheme}*.disabledText = @disabledForeground
{Monokai_Pro---Subtheme}*.disabledForeground = @disabledForeground
{Monokai_Pro---Subtheme}*.inactiveForeground = @disabledForeground
{Monokai_Pro---Subtheme}ProgressBar.selectionBackground = #fff
{Monokai_Pro---Subtheme}Table.selectionInactiveForeground = $List.selectionInactiveForeground
{Monokai_Pro---Subtheme}Tree.selectionBackground = $List.selectionBackground
{Monokai_Pro---Subtheme}ToggleButton.foreground = $Button.foreground
{Monokai_Pro---Subtheme}Separator.foreground = @ijSeparatorDark
{Monokai_Pro---Subtheme}ToolBar.separatorColor = $Separator.foreground
{Monokai_Pro---Subtheme}ToolBar.background = @background

{Nord}@disabledForeground = #616E88
{Nord}*.disabledText = @disabledForeground
{Nord}*.disabledForeground = @disabledForeground
{Nord}*.inactiveForeground = @disabledForeground
{Nord}MenuItem.checkBackground = @ijMenuCheckBackgroundL10
{Nord}MenuItem.underlineSelectionCheckBackground = @ijMenuCheckBackgroundL10
{Nord}RadioButtonMenuItem.selectionBackground = $MenuItem.selectionBackground
{Nord}ProgressBar.selectionBackground = @foreground
{Nord}ProgressBar.selectionForeground = @background
{Nord}List.selectionBackground = $Tree.selectionBackground
{Nord}List.selectionForeground = $Tree.selectionForeground
{Nord}Table.selectionBackground = $Tree.selectionBackground
{Nord}Table.selectionForeground = $Tree.selectionForeground
{Nord}TextField.selectionBackground = $Tree.selectionBackground
{Nord}TextField.selectionForeground = $Tree.selectionForeground
{Nord}Tree.selectionInactiveForeground = $List.selectionInactiveForeground

{NotReallyMDTheme}*.selectionInactiveBackground = #21384E
{NotReallyMDTheme}ToolBar.separatorColor = $Separator.foreground

{One_Dark}MenuItem.checkBackground = @ijMenuCheckBackgroundL10
{One_Dark}MenuItem.underlineSelectionCheckBackground = @ijMenuCheckBackgroundL10
{One_Dark}ProgressBar.background = $Separator.foreground
{One_Dark}ProgressBar.selectionForeground = #fff
{One_Dark}Table.background = $Tree.background
{One_Dark}Table.selectionBackground = $Tree.selectionBackground
{One_Dark}TextField.selectionBackground = $List.selectionBackground
{One_Dark}Tree.selectionForeground = $List.selectionForeground

{Solarized_Dark---4lex4}@accentBaseColor = $TabbedPane.underlineColor
{Solarized_Dark---4lex4}*.acceleratorForeground = @menuAcceleratorForeground
{Solarized_Dark---4lex4}ComboBox.background = $ComboBox.editableBackground
{Solarized_Dark---4lex4}ComboBox.buttonBackground = $ComboBox.editableBackground
{Solarized_Dark---4lex4}Slider.disabledTrackColor = $ColorPalette.colorSeparator

{Solarized_Light---4lex4}@accentBaseColor = $TabbedPane.underlineColor
{Solarized_Light---4lex4}Slider.thumbColor = $ProgressBar.foreground
{Solarized_Light---4lex4}Slider.disabledTrackColor = $ColorPalette.colorSeparator

{Spacegray}ComboBox.background = @ijTextBackgroundL4
{Spacegray}ComboBox.buttonBackground = $ComboBox.background
{Spacegray}TextField.background = @ijTextBackgroundL4

{vuesion-theme}@accentBaseColor = $TabbedPane.underlineColor
{vuesion-theme}@disabledForeground = #8C8C8C
{vuesion-theme}*.disabledText = @disabledForeground
{vuesion-theme}*.disabledForeground = @disabledForeground
{vuesion-theme}*.inactiveForeground = @disabledForeground
{vuesion-theme}MenuItem.checkBackground = @ijMenuCheckBackgroundL10
{vuesion-theme}MenuItem.underlineSelectionCheckBackground = @ijMenuCheckBackgroundL10
{vuesion-theme}ProgressBar.background = #303a45
{vuesion-theme}ProgressBar.foreground = #ececee
{vuesion-theme}Slider.thumbColor = #ececee
{vuesion-theme}Slider.focusedColor = fade($Slider.thumbColor,20%)
{vuesion-theme}ComboBox.background = @ijTextBackgroundL4
{vuesion-theme}ComboBox.buttonBackground = $ComboBox.background
{vuesion-theme}TextField.background = @ijTextBackgroundL4
{vuesion-theme}TextField.selectionBackground = lighten(#303A45,15%)

{Xcode-Dark}@accentBaseColor = $List.selectionBackground
{Xcode-Dark}TextField.background = @ijTextBackgroundL4


# Material Theme UI Lite

{author-Mallowigi}[light]controlHighlight = lighten($controlShadow,8%)
{author-Mallowigi}[light]controlLtHighlight = lighten($controlShadow,15%)
{author-Mallowigi}[light]controlDkShadow = darken($controlShadow,15%)
{author-Mallowigi}[dark]controlHighlight = darken($controlShadow,10%)
{author-Mallowigi}[dark]controlLtHighlight = darken($controlShadow,15%)
{author-Mallowigi}[dark]controlDkShadow = lighten($controlShadow,10%)

{author-Mallowigi}Button.hoverBorderColor = $Button.focusedBorderColor
{author-Mallowigi}HelpButton.hoverBorderColor = $Button.focusedBorderColor

{author-Mallowigi}[light]ToggleButton.selectedForeground = #000
{author-Mallowigi}[dark]ToggleButton.selectedForeground = #fff

{author-Mallowigi}[light]MenuItem.checkBackground = @ijMenuCheckBackgroundD10
{author-Mallowigi}[light]MenuItem.underlineSelectionCheckBackground = @ijMenuCheckBackgroundD10
{author-Mallowigi}[dark]MenuItem.checkBackground = @ijMenuCheckBackgroundL20
{author-Mallowigi}[dark]MenuItem.underlineSelectionCheckBackground = @ijMenuCheckBackgroundL20

{author-Mallowigi}[light]Separator.foreground = @ijSeparatorLight
{author-Mallowigi}[dark]Separator.foreground = @ijSeparatorDark
{author-Mallowigi}ProgressBar.selectionBackground = @foreground
{author-Mallowigi}TabbedPane.selectedBackground = mix(@background,$ColorPalette.table,60%)
{author-Mallowigi}ToolBar.separatorColor = $Separator.foreground
{author-Mallowigi}Button.foreground = @foreground
{author-Mallowigi}Tree.foreground = @foreground


{Arc_Dark}ComboBox.selectionBackground = $List.selectionBackground
{Arc_Dark}ProgressBar.selectionBackground = #fff
{Arc_Dark}ProgressBar.selectionForeground = #fff
{Arc_Dark}Table.selectionBackground = $List.selectionBackground
{Arc_Dark}Tree.selectionInactiveBackground = $List.selectionInactiveBackground

{Atom_One_Dark}ProgressBar.selectionBackground = #fff
{Atom_One_Dark}ProgressBar.selectionForeground = #fff
{Atom_One_Dark}List.selectionBackground = $Table.selectionBackground
{Atom_One_Dark}Tree.selectionBackground = $Table.selectionBackground
{Atom_One_Dark}List.selectionInactiveBackground = $Tree.selectionInactiveBackground
{Atom_One_Dark}Table.selectionInactiveBackground = $Tree.selectionInactiveBackground

{Atom_One_Light}@disabledForeground = shade($ColorPalette.dis,20%)
{Atom_One_Light}*.disabledText = @disabledForeground
{Atom_One_Light}*.disabledForeground = @disabledForeground
{Atom_One_Light}*.inactiveForeground = @disabledForeground
{Atom_One_Light}TabbedPane.contentAreaColor = $Separator.foreground

{Dracula---Mallowigi}ProgressBar.selectionBackground = #fff
{Dracula---Mallowigi}ProgressBar.selectionForeground = #fff
{Dracula---Mallowigi}List.selectionBackground = $Table.selectionBackground
{Dracula---Mallowigi}Tree.selectionBackground = $Table.selectionBackground
{Dracula---Mallowigi}List.selectionInactiveBackground = $Tree.selectionInactiveBackground
{Dracula---Mallowigi}Table.selectionInactiveBackground = $Tree.selectionInactiveBackground

{GitHub}ProgressBar.selectionBackground = #222
{GitHub}ProgressBar.selectionForeground = #222
{GitHub}TextField.background = @ijTextBackgroundL3
{GitHub}List.selectionBackground = $Table.selectionBackground
{GitHub}Tree.selectionBackground = $Table.selectionBackground
{GitHub}List.selectionInactiveBackground = $Tree.selectionInactiveBackground
{GitHub}Table.selectionInactiveBackground = $Tree.selectionInactiveBackground

{GitHub_Dark}ComboBox.selectionBackground = $Tree.selectionBackground
{GitHub_Dark}ProgressBar.selectionForeground = #fff
{GitHub_Dark}Slider.trackColor = lighten(#2b3036,5%)
{GitHub_Dark}Table.selectionBackground = $Tree.selectionBackground
{GitHub_Dark}Tree.selectionInactiveBackground = $Table.selectionInactiveBackground

{Light_Owl}@disabledForeground = shade($ColorPalette.dis,10%)
{Light_Owl}*.disabledText = @disabledForeground
{Light_Owl}*.disabledForeground = @disabledForeground
{Light_Owl}*.inactiveForeground = @disabledForeground
{Light_Owl}CheckBoxMenuItem.selectionForeground = $CheckBoxMenuItem.foreground
{Light_Owl}ComboBox.selectionForeground = $ComboBox.foreground
{Light_Owl}List.selectionInactiveForeground = $Table.selectionInactiveForeground
{Light_Owl}Menu.selectionForeground = $Menu.foreground
{Light_Owl}MenuBar.selectionForeground = $MenuBar.foreground
{Light_Owl}MenuItem.selectionForeground = $MenuItem.foreground
{Light_Owl}Table.selectionForeground = $List.selectionForeground
{Light_Owl}TextField.selectionForeground = $TextField.foreground
{Light_Owl}TextField.background = @ijTextBackgroundL3
{Light_Owl}List.selectionBackground = $Table.selectionBackground
{Light_Owl}Tree.selectionBackground = $Table.selectionBackground
{Light_Owl}List.selectionInactiveBackground = $Tree.selectionInactiveBackground
{Light_Owl}Table.selectionInactiveBackground = $Tree.selectionInactiveBackground

{Material_Darker}@disabledForeground = tint($ColorPalette.dis,30%)
{Material_Darker}*.disabledText = @disabledForeground
{Material_Darker}*.disabledForeground = @disabledForeground
{Material_Darker}*.inactiveForeground = @disabledForeground
{Material_Darker}*.selectionBackground = lighten($ColorPalette.tree,15%)
{Material_Darker}ProgressBar.selectionForeground = #fff
{Material_Darker}Tree.selectionInactiveBackground = $List.selectionInactiveBackground

{Material_Deep_Ocean}@disabledForeground = tint($ColorPalette.dis,10%)
{Material_Deep_Ocean}*.disabledText = @disabledForeground
{Material_Deep_Ocean}*.disabledForeground = @disabledForeground
{Material_Deep_Ocean}*.inactiveForeground = @disabledForeground
{Material_Deep_Ocean}*.selectionBackground = lighten($ColorPalette.tree,15%)
{Material_Deep_Ocean}ProgressBar.selectionBackground = #fff
{Material_Deep_Ocean}Slider.trackColor = lighten(#1A1C25,5%)
{Material_Deep_Ocean}Tree.selectionInactiveBackground = $List.selectionInactiveBackground

{Material_Lighter}@disabledForeground = shade($ColorPalette.dis,30%)
{Material_Lighter}*.disabledText = @disabledForeground
{Material_Lighter}*.disabledForeground = @disabledForeground
{Material_Lighter}*.inactiveForeground = @disabledForeground
{Material_Lighter}ComboBox.selectionBackground = $List.selectionBackground
{Material_Lighter}List.selectionForeground = $Table.selectionForeground
{Material_Lighter}List.selectionInactiveForeground = $Table.selectionInactiveForeground
{Material_Lighter}ProgressBar.selectionBackground = #222
{Material_Lighter}RadioButtonMenuItem.selectionForeground = $Table.selectionForeground
{Material_Lighter}Table.selectionBackground = $List.selectionBackground
{Material_Lighter}Tree.selectionForeground = $Table.selectionForeground
{Material_Lighter}Tree.selectionInactiveBackground = $List.selectionInactiveBackground

{Material_Oceanic}@disabledForeground = tint($ColorPalette.dis,30%)
{Material_Oceanic}*.disabledText = @disabledForeground
{Material_Oceanic}*.disabledForeground = @disabledForeground
{Material_Oceanic}*.inactiveForeground = @disabledForeground
{Material_Oceanic}ProgressBar.selectionBackground = #ddd
{Material_Oceanic}ProgressBar.selectionForeground = #ddd
{Material_Oceanic}List.selectionBackground = $Table.selectionBackground
{Material_Oceanic}Tree.selectionBackground = $Table.selectionBackground
{Material_Oceanic}List.selectionInactiveBackground = $Tree.selectionInactiveBackground
{Material_Oceanic}Table.selectionInactiveBackground = $Tree.selectionInactiveBackground

{Material_Palenight}@disabledForeground = tint($ColorPalette.dis,20%)
{Material_Palenight}*.disabledText = @disabledForeground
{Material_Palenight}*.disabledForeground = @disabledForeground
{Material_Palenight}*.inactiveForeground = @disabledForeground
{Material_Palenight}ProgressBar.selectionBackground = #ddd
{Material_Palenight}ProgressBar.selectionForeground = #ddd
{Material_Palenight}List.selectionBackground = $Table.selectionBackground
{Material_Palenight}Tree.selectionBackground = $Table.selectionBackground
{Material_Palenight}List.selectionInactiveBackground = $Tree.selectionInactiveBackground
{Material_Palenight}Table.selectionInactiveBackground = $Tree.selectionInactiveBackground

{Monokai_Pro---Mallowigi}@disabledForeground = tint($ColorPalette.dis,20%)
{Monokai_Pro---Mallowigi}*.disabledText = @disabledForeground
{Monokai_Pro---Mallowigi}*.disabledForeground = @disabledForeground
{Monokai_Pro---Mallowigi}*.inactiveForeground = @disabledForeground
{Monokai_Pro---Mallowigi}RadioButtonMenuItem.selectionForeground = $MenuItem.selectionForeground
{Monokai_Pro---Mallowigi}List.selectionForeground = $Table.selectionForeground
{Monokai_Pro---Mallowigi}Tree.selectionForeground = $Table.selectionForeground
{Monokai_Pro---Mallowigi}List.selectionInactiveForeground = $Table.selectionInactiveForeground
{Monokai_Pro---Mallowigi}List.selectionBackground = $Table.selectionBackground
{Monokai_Pro---Mallowigi}Tree.selectionBackground = $Table.selectionBackground
{Monokai_Pro---Mallowigi}List.selectionInactiveBackground = $Tree.selectionInactiveBackground
{Monokai_Pro---Mallowigi}Table.selectionInactiveBackground = $Tree.selectionInactiveBackground

{Moonlight}ComboBox.selectionBackground = $List.selectionBackground
{Moonlight}ProgressBar.selectionForeground = #000
{Moonlight}Table.selectionBackground = $List.selectionBackground
{Moonlight}Tree.selectionInactiveBackground = $List.selectionInactiveBackground

{Solarized_Dark---Mallowigi}@disabledForeground = tint($ColorPalette.dis,20%)
{Solarized_Dark---Mallowigi}*.disabledForeground = @disabledForeground
{Solarized_Dark---Mallowigi}*.inactiveForeground = @disabledForeground
{Solarized_Dark---Mallowigi}*.disabledText = @disabledForeground
{Solarized_Dark---Mallowigi}ProgressBar.selectionBackground = #ccc
{Solarized_Dark---Mallowigi}ProgressBar.selectionForeground = #ccc
{Solarized_Dark---Mallowigi}Slider.trackColor = lighten(@background,10%)
{Solarized_Dark---Mallowigi}Table.selectionBackground = $List.selectionBackground
{Solarized_Dark---Mallowigi}Tree.selectionInactiveBackground = $List.selectionInactiveBackground

{Solarized_Light---Mallowigi}@disabledForeground = tint(@foreground,30%)
{Solarized_Light---Mallowigi}*.disabledForeground = @disabledForeground
{Solarized_Light---Mallowigi}*.inactiveForeground = @disabledForeground
{Solarized_Light---Mallowigi}*.disabledText = @disabledForeground
{Solarized_Light---Mallowigi}ProgressBar.selectionBackground = #222
{Solarized_Light---Mallowigi}ComboBox.selectionBackground = $List.selectionBackground
{Solarized_Light---Mallowigi}Slider.disabledTrackColor = lighten($Slider.trackColor,5%)
{Solarized_Light---Mallowigi}Table.selectionBackground = $List.selectionBackground
{Solarized_Light---Mallowigi}Tree.selectionInactiveBackground = $List.selectionInactiveBackground
{Solarized_Light---Mallowigi}Button.toolbar.selectedBackground = darken($@background,15%)
{Solarized_Light---Mallowigi}ToggleButton.toolbar.selectedBackground = $Button.toolbar.selectedBackground
