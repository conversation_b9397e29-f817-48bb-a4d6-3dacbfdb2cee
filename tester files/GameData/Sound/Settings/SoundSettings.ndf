// Toutes les distances dans ce fichier sont en mètres.
//
//
//  Spatialized                 Active la spatialisation du son.
//  UseMicroCanon         Active l'atténuation des sons hors-champ.
//  ChannelRange          Requis: Reserve un intervalle de canaux pour ce TSoundSettings.
//                        Plusieurs TSoundSettings peuvent partager des canaux, mais ils pourront alors interférer les un avec les autres
//                        Cette reservation n'existe que pour les sons 2D et est ignorées pour les sons 3D.
//
//  FadeDistanceNear      Distance à la source à partir de laquelle l'attenuation du volume commence.
//  FadeDistanceFar       Distance à la source au dela de laquelle l'atténuation du volume est maximale (=son inaudible).
//
//  UseLowPass            Active l'étouffement progressif du son selon l'éloignement.
//  LowPassDistanceNear   Distance à la source en dessous de laquelle aucun étouffement n'est appliqué.
//  LowPassDistanceFar    Distance à la source au dessus de laquelle l'étouffement est maximal.
//
//  DopplerSettings       Spécifie le TDopplerSettings à utiliser (optionel).

export GlobalDopplerSettings is TDopplerSettings
(
    SoundCelerity = 340 // m/s
    PitchVelocity = 100.0
    PitchRange = [ 0.1, 10.0 ]
)

export HelicoDopplerSettings is TDopplerSettings
(
    SoundCelerity = 340 // m/s
    PitchVelocity = 25.0
    PitchRange = [ 1.0, 1.6 ]
)

export AcknowUnit_SoundSettings is TSoundSettings
(
    IgnoreDynamicCompression = True
    Pitch = 1

    Fader = ~/AcknowUnitFader
    ChannelRange = [0, 0]
)

export Dialog_SoundSettings is TSoundSettings
(
    ChannelRange = [1, 2]
    IgnoreDynamicCompression = True
    Pitch = 1

    Fader = ~/DialogGfxFader
)

export HUD_SoundSettings is TSoundSettings
(
    IgnoreDynamicCompression = True
    Pitch = 1
    ChannelRange = [3, 7]

    Fader = ~/HudVolumeControlFader
)

export Ambiance_2D_SoundSettings is TSoundSettings
(
    Pitch = 1
    ChannelRange = [3, 7]

    Fader = ~/Ambience2DFader
)

export SoundSettings_Strategic_Move is TSoundSettings
(
    ChannelRange = [3, 7]
    Pitch = 1
    RandomPitch = 0.1

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Steelman_Combat is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.1

    Spatialized = True
    FadeDistanceNear = 4.5
    FadeDistanceFar  = 20000

    UseLowPass = True
    LowPassDistanceNear = 5000
    LowPassDistanceFar  = 10000

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Ambiance_City_Evenements is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.05

    Spatialized = True
    FadeDistanceNear =    5
    FadeDistanceFar  = 9300

    UseLowPass = True
    LowPassDistanceNear =  230
    LowPassDistanceFar  = 2800

    Fader = ~/GlobalAmbienceFader
)

export SoundSettings_Ambiance_HLM_Evenements is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.05

    Spatialized = True
    FadeDistanceNear =    5
    FadeDistanceFar  = 14000 //9300

    UseLowPass = True
    LowPassDistanceNear =  230
    LowPassDistanceFar  = 4200 //2800

    Fader = ~/GlobalAmbienceFader
)

export SoundSettings_Ambiance_Ferme_Evenements is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.05

    Spatialized = True
    FadeDistanceNear =  4.5
    FadeDistanceFar  = 1850

    UseLowPass = True
    LowPassDistanceNear =  95
    LowPassDistanceFar  = 930

    Fader = ~/GlobalAmbienceFader
)

export SoundSettings_Ambiance_Indus_Evenements is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.05

    Spatialized = True
    FadeDistanceNear = 4.5
    FadeDistanceFar  = 1850

    UseLowPass = True
    LowPassDistanceNear = 95
    LowPassDistanceFar  = 930

    Fader = ~/GlobalAmbienceFader
)

export SoundSettings_Ambiance_City is TSoundSettings
(
    Pitch = 1

    Spatialized = True
    FadeDistanceNear = 4.5
    FadeDistanceFar  = 4600

    UseLowPass = True
    LowPassDistanceNear = 95
    LowPassDistanceFar  = 465

    Fader = ~/GlobalAmbienceFader
)

export SoundSettings_Ambiance_Eau is TSoundSettings
(
    Pitch = 1

    Spatialized = True
    FadeDistanceFar  = 9300
    FadeDistanceNear = 4.5

    UseLowPass = True
    LowPassDistanceFar  = 1850
    LowPassDistanceNear = 185

    Fader = ~/GlobalAmbienceFader
)

export SoundSettings_Ambiance_Ferme is TSoundSettings
(
    Pitch = 1

    Spatialized = True
    FadeDistanceFar  = 1400
    FadeDistanceNear = 4.5

    UseLowPass = True
    LowPassDistanceFar  = 230
    LowPassDistanceNear = 95

    Fader = ~/GlobalAmbienceFader
)

export SoundSettings_Ambiance_Indus is TSoundSettings
(
    Pitch = 1
    Spatialized = True

    FadeDistanceFar  = 1850
    FadeDistanceNear = 4.5

    UseLowPass = True
    LowPassDistanceFar  = 370
    LowPassDistanceNear = 95

    Fader = ~/GlobalAmbienceFader
)

export SoundSettings_Moteur is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.25

    Spatialized = True
    UseFogOfWar = True
    FadeDistanceNear = 0
    FadeDistanceFar  = 2300

    UseLowPass = True
    LowPassDistanceNear =   2
    LowPassDistanceFar  = 300

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Moteur_Helico is TSoundSettings
(
    Pitch = 1 //2
    RandomPitch = 0.25  //0.5

    Spatialized = True
    UseFogOfWar = True
    LowPassDistanceNear = 115
    LowPassDistanceFar  = 700

    UseLowPass = True
    FadeDistanceNear = 1
    FadeDistanceFar  = 3000 //9300

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Moteur_Helico_Low_Pitch is TSoundSettings
(
    Pitch = 1 //2
    RandomPitch = 0.15 //0.5

    Spatialized = True
    UseFogOfWar = True
    FadeDistanceFar  = 3000 //9300
    FadeDistanceNear = 1

    UseLowPass = True
    LowPassDistanceFar  = 700
    LowPassDistanceNear = 115

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Moteur_Avion is TSoundSettings
(
    Pitch = 1 //2
    RandomPitch = 0.25 //0.5

    Spatialized = True
    UseFogOfWar = True
    FadeDistanceFar  = 7000
    FadeDistanceNear = 4.5

    UseLowPass = True
    LowPassDistanceFar  = 2300
    LowPassDistanceNear = 230

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Default is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 7000
    FadeDistanceNear = 0

    UseLowPass = True
    LowPassDistanceFar  = 1600
    LowPassDistanceNear = 160

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Deplacement_Infanterie is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.2

    Spatialized = True
    UseFogOfWar = True
    FadeDistanceFar  = 1300 //375
    FadeDistanceNear = 10

    UseLowPass = True
    LowPassDistanceFar  = 100 //280
    LowPassDistanceNear = 10

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Impact_Balles_legeres is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.5 //0.2

    Spatialized = True
    FadeDistanceFar  = 800 //465
    FadeDistanceNear = 100 //0

    UseLowPass = True
    LowPassDistanceFar  = 200 //375
    LowPassDistanceNear = 50 //18

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Impact_Balles is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.5 /// 0.2

    Spatialized = True
    FadeDistanceFar  = 800
    FadeDistanceNear = 100

    UseLowPass = True
    LowPassDistanceFar  = 200
    LowPassDistanceNear = 50

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tir_Fusil_Mitrailleur is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 30000  //25000
    FadeDistanceNear = 200 //0

    UseLowPass = True
    LowPassDistanceFar  = 750 //700
    LowPassDistanceNear = 100 //0

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tir_Mitrailleuse_legere is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 30000 //4600
    FadeDistanceNear = 200

    UseLowPass = True
    LowPassDistanceFar  = 750 //930
    LowPassDistanceNear = 100 //100

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tourelles is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.1

    Spatialized = True
    FadeDistanceFar  = 250
    FadeDistanceNear = 0

    UseLowPass = True
    UseFogOfWar = True
    LowPassDistanceFar  = 110
    LowPassDistanceNear = 5

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tir_Mitrailleuse_lourde is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 30000
    FadeDistanceNear = 200

    UseLowPass = True
    LowPassDistanceFar  = 1000
    LowPassDistanceNear = 100 //100

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tir_Autocanon is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 7000
    FadeDistanceNear = 200 //0

    UseLowPass = True
    LowPassDistanceFar  = 1500
    LowPassDistanceNear = 150

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tir_Canon_Moyen is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 7000
    FadeDistanceNear = 2.5

    UseLowPass = True
    LowPassDistanceFar  = 2300
    LowPassDistanceNear = 140

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tir_Canon_Gros is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 9500
    FadeDistanceNear = 4.5

    UseLowPass = True
    LowPassDistanceFar  = 2800
    LowPassDistanceNear = 230

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Propulsion_Missile_Petit is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 2500
    FadeDistanceNear = 0

    UseLowPass = True
    LowPassDistanceFar  = 800
    LowPassDistanceNear = 115

    DopplerSettings = GlobalDopplerSettings

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Propulsion_Missile_Moyen is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 4000
    FadeDistanceNear = 0

    UseLowPass = True
    LowPassDistanceFar  = 1000
    LowPassDistanceNear = 250

    DopplerSettings = GlobalDopplerSettings

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Propulsion_Missile_Gros is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.5 //0.15

    Spatialized = True
    FadeDistanceFar  = 9500
    FadeDistanceNear = 0

    UseLowPass = True
    LowPassDistanceFar  = 3500
    LowPassDistanceNear = 45

    DopplerSettings = GlobalDopplerSettings

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tir_Missile_Moyen is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 7000
    FadeDistanceNear = 0

    UseLowPass = True
    LowPassDistanceFar  = 2300
    LowPassDistanceNear = 140

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tir_Missile_Gros is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 9500
    FadeDistanceNear = 0

    UseLowPass = True
    LowPassDistanceFar  = 1500
    LowPassDistanceNear = 100

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_MLRS is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceFar  = 18000
    FadeDistanceNear = 0

    UseLowPass = True
    LowPassDistanceFar  = 8000
    LowPassDistanceNear = 115

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Mort_Unite_Petite is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceNear = 0
    FadeDistanceFar  = 9300

    UseLowPass = True
    LowPassDistanceNear =   95
    LowPassDistanceFar  = 2100

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Mort_Unite_Grosse is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceNear =     0
    FadeDistanceFar  = 11600

    UseLowPass = True
    LowPassDistanceNear =  100
    LowPassDistanceFar  = 2300

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Impact_SAM is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceNear =     0
    FadeDistanceFar  = 28000

    UseLowPass = True
    LowPassDistanceNear =   23
    LowPassDistanceFar  = 4600

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tres_Petits_Impact_HE is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceNear = 0
    FadeDistanceFar  = 7000

    UseLowPass = True
    LowPassDistanceNear = 15
    LowPassDistanceFar  = 1000

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Petits_Impact_HE is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceNear =    0
    FadeDistanceFar  = 8000

    UseLowPass = True
    LowPassDistanceNear =   10
    LowPassDistanceFar  = 1000

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Moyen_Impact_HE is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceNear =    0
    FadeDistanceFar  = 9000

    UseLowPass = True
    LowPassDistanceNear =   15
    LowPassDistanceFar  = 1500

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Gros_Impact_HE is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceNear =     0
    FadeDistanceFar  = 18600

    UseLowPass = True
    LowPassDistanceNear =   23
    LowPassDistanceFar  = 4500

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Tres_Gros_Impact_HE is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceNear =     0
    FadeDistanceFar  = 23000

    UseLowPass = True
    LowPassDistanceNear =   50
    LowPassDistanceFar  = 5000

    Fader = ~/FXVolumeControlFader
)

export SoundSettings_Nuke is TSoundSettings
(
    Pitch = 1
    RandomPitch = 0.15

    Spatialized = True
    FadeDistanceNear =    465
    FadeDistanceFar  = 230000

    UseLowPass = True
    LowPassDistanceNear =  2300
    LowPassDistanceFar  = 14000

    Fader = ~/FXVolumeControlFader
)


