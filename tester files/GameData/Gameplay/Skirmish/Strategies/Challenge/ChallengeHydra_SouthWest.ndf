//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------

export ChallengeHydra_SouthWest_Strategy is TIAGeneralStrategy
(
    StrategyName = "HydraAIsw"
    FirstGenerator = ChalHydra_SW_Choose_Strategy
    AllowToByPassNbMacroActionAllowedToProduceSimultaneous = true
    AuthorizedCorridorList = [0]
    TransitionList =
    [
        ChalHydra_SW_Transition_StartToPhaseCommand,
        ChalHydra_SW_Transition_StartToGeneric,
        ChalHydra_SW_Transition_DeployToCommandPhase,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalHydra_SW_Condition_GotoGeneric is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export ChalHydra_SW_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)

//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalHydra_SW_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = ChalHydra_SW_Choose_Strategy
    Condition = ChalHydra_SW_Condition_CommandPhase
    Destination = ChalHydra_SW_Phase_Command
)
export ChalHydra_SW_Transition_StartToGeneric is TIAGeneralStrategyTransition
(
    Origine = ChalHydra_SW_Choose_Strategy
    Condition = ChalHydra_SW_Condition_GotoGeneric
    Destination = ChalHydra_SW_Phase_Deploiement
)
//-------------------------------------------------------------------------
export ChalHydra_SW_Transition_DeployToCommandPhase is TIAGeneralStrategyTransition
(
    Origine = ChalHydra_SW_Phase_Deploiement
    Condition = ChalHydra_SW_Condition_CommandPhase
    Destination = ChalHydra_SW_Phase_Command
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export ChalHydra_SW_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    NbMacroActionAllowedToProduceSimultaneous = 9001
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,
    ]
)
//-------------------------------------------------------------------------
export ChalHydra_SW_Phase_Deploiement is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 4
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,

        ~/CaptureOneObjectif,

        ~/Challenge_Hydra_Defense_Objectif,
        ~/Challenge_Hydra_Main_Attack,
        ~/Challenge_Hydra_Main_Attack,

        ~/CaptureAllZoneObjectif,
    ]

    ScalingGeneratorList = ~/ChalHydra_SW_Scaling
)
//-------------------------------------------------------------------------
export ChalHydra_SW_Phase_Command is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 3
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,

        ~/CaptureOneObjectif,

        ~/Challenge_Hydra_Defense_Objectif,
        ~/Challenge_Hydra_Main_Attack,
        ~/Challenge_Hydra_Main_Attack,

        ~/CaptureAllZoneObjectif,
    ]

    ScalingGeneratorList = ~/ChalHydra_SW_Scaling
)

ChalHydra_SW_Scaling is
[
    ~/Challenge_Hydra_Main_Attack,
]