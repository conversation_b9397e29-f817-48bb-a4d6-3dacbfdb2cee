//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export ChallengeGenericDefenseStrategy is TIAGeneralStrategy
(
    StrategyName = "ChalDefAI"
    FirstGenerator = ChalDefenseGeneric_Choose_Strategy
    AllowToByPassNbMacroActionAllowedToProduceSimultaneous = true
    TransitionList =
    [
        ChalDefenseGeneric_Transition_StartToPhaseCommand,
        ChalDefenseGeneric_Transition_StartToGeneric,
        ChalDefenseGeneric_Transition_DeployToCommandPhase,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalDefenseGeneric_Condition_GotoGeneric is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export ChalDefenseGeneric_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)

//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalDefenseGeneric_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = ChalDefenseGeneric_Choose_Strategy
    Condition = ChalDefenseGeneric_Condition_CommandPhase
    Destination = ChalDefenseGeneric_Phase_Command
)
export ChalDefenseGeneric_Transition_StartToGeneric is TIAGeneralStrategyTransition
(
    Origine = ChalDefenseGeneric_Choose_Strategy
    Condition = ChalDefenseGeneric_Condition_GotoGeneric
    Destination = ChalDefenseGeneric_Phase_Deploiement
)
//-------------------------------------------------------------------------
export ChalDefenseGeneric_Transition_DeployToCommandPhase is TIAGeneralStrategyTransition
(
    Origine = ChalDefenseGeneric_Phase_Deploiement
    Condition = ChalDefenseGeneric_Condition_CommandPhase
    Destination = ChalDefenseGeneric_Phase_Command
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export ChalDefenseGeneric_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    NbMacroActionAllowedToProduceSimultaneous = 9001
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,
    ]
)
//-------------------------------------------------------------------------
export ChalDefenseGeneric_Phase_Deploiement is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 4
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,

        ~/CaptureOneObjectif, //Dependant du deck estimation 125

        ~/Attack_Objectives, //300

        ~/Defense_Objectif, //Chere mais dépendant du nombre d'objectif et de la capture

        ~/Challenge_Defense_Corridor_Filler,
        ~/Defense_Corridor_Adaptive,

        ~/Airstrike_AA,
        ~/Artillery_TriggerHappy_1for2Corridors,

        ~/CaptureAllZoneObjectif,

        ~/Artillery_Expensive,
        ~/Airstrike_SEAD,
    ]

    ScalingGeneratorList = ~/Challenge_Defense_Scaling
)
//-------------------------------------------------------------------------
export ChalDefenseGeneric_Phase_Command is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 3
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,

        ~/CaptureOneObjectif, //Dependant du deck estimation 125

        ~/Attack_Objectives, //300

        ~/Defense_Objectif, //Chere mais dépendant du nombre d'objectif et de la capture

        ~/Challenge_Defense_Corridor_Filler,
        ~/Defense_Corridor_Adaptive,

        ~/Airstrike_AA,
        ~/Artillery_TriggerHappy_1for2Corridors,

        ~/CaptureAllZoneObjectif,

        ~/Artillery_Expensive,
        ~/Airstrike_SEAD,
    ]
    ScalingGeneratorList = ~/Challenge_Defense_Scaling
)

Challenge_Defense_Scaling is
[
    ~/Challenge_Defense_Corridor_Filler,
    ~/Defense_Corridor_Adaptive,
    ~/Defense_Objectif,
    ~/Artillery_TriggerHappy_1for2Corridors,
    ~/Airstrike_AA,
]
