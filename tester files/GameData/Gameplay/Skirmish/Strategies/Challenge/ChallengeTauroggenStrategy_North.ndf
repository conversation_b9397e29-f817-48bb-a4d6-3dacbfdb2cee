//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export ChallengeTauroggenStrategy_North is TIAGeneralStrategy
(
    StrategyName = "TaurNorAI"
    FirstGenerator = ChalTauroNorth_Choose_Strategy
    AuthorizedCorridorList = [1]
    AllowToByPassNbMacroActionAllowedToProduceSimultaneous = true
    TransitionList =
    [
        ChalTauroNorth_Transition_StartToPhaseCommand,
        ChalTauroNorth_Transition_StartToGeneric,
        ChalTauroNorth_Transition_DeployToCommandPhase,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalTauroNorth_Condition_GotoGeneric is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export ChalTauroNorth_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)

//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalTauroNorth_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = ChalTauroNorth_Choose_Strategy
    Condition = ChalTauroNorth_Condition_CommandPhase
    Destination = ChalTauroNorth_Phase_Command
)
export ChalTauroNorth_Transition_StartToGeneric is TIAGeneralStrategyTransition
(
    Origine = ChalTauroNorth_Choose_Strategy
    Condition = ChalTauroNorth_Condition_GotoGeneric
    Destination = ChalTauroNorth_Phase_Deploiement
)
//-------------------------------------------------------------------------
export ChalTauroNorth_Transition_DeployToCommandPhase is TIAGeneralStrategyTransition
(
    Origine = ChalTauroNorth_Phase_Deploiement
    Condition = ChalTauroNorth_Condition_CommandPhase
    Destination = ChalTauroNorth_Phase_Command
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export ChalTauroNorth_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    NbMacroActionAllowedToProduceSimultaneous = 9001
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,
    ]
)
//-------------------------------------------------------------------------
export ChalTauroNorth_Phase_Deploiement is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 4
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Challenge_Main_Attack_All,
    ]
)
//-------------------------------------------------------------------------
export ChalTauroNorth_Phase_Command is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 3
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Challenge_Main_Attack_All,
        ~/CaptureAllZoneObjectif,
    ]
)
