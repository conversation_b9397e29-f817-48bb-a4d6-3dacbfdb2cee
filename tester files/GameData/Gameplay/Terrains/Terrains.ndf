export DefaultTerrain is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = false
    BloqueInfanterie = false
    BloqueVehicule = false
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[0, 0, 0, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0
    Name = 'DefaultTerrain'
    SpeedModifierAllTerrainWheel = 1
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 1
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/Default
    AuthorizeNearGroundFlying = true
)
export ForetDense is TGameplayTerrain
(
    BloqueAmphibie = true
    BloqueAtterrissage = true
    BloqueInfanterie = false
    BloqueVehicule = true
    BloqueVision = true
    CriticalEffectProbability = 0.05
    DamageModifierPerFamilyAndResistance =
    MAP [
            (DamageFamily_he, MAP [(ResistanceFamily_infanterie,0.6)]),
            (DamageFamily_fmballe, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_balledca, MAP [(ResistanceFamily_infanterie,0.5)]),
        ]
    DebugColor = RGBA[2, 106, 2, 255]
    DissimulationModifierGroundAir = 20
    DissimulationModifierGroundGround = 24
    HeightInMeters = 11
    InflammabilityProbability = 0.8
    Name = 'ForetDense'
    SpeedModifierAllTerrainWheel = 0
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 0
    ConcealmentBonus = 3
    TerrainType = ~/ETerrainType/ForetDense
    AuthorizeNearGroundFlying = false
)
export ForetLegere is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = true
    BloqueInfanterie = false
    BloqueVehicule = false
    BloqueVision = false
    CriticalEffectProbability = 0.025
    DamageModifierPerFamilyAndResistance =
    MAP [
            (DamageFamily_he, MAP [(ResistanceFamily_infanterie,0.65)]),
            (DamageFamily_he_autocanon, MAP [(ResistanceFamily_infanterie,0.65)]),
            (DamageFamily_superhe, MAP [(ResistanceFamily_infanterie,0.65)]),
            (DamageFamily_balle, MAP [(ResistanceFamily_infanterie,0.65)]),
            (DamageFamily_balledca, MAP [(ResistanceFamily_infanterie,0.65)]),
            (DamageFamily_howz, MAP [(ResistanceFamily_infanterie,0.65)]),
            (DamageFamily_howz_bombe, MAP [(ResistanceFamily_infanterie,0.65)]),
            (DamageFamily_fmballe, MAP [(ResistanceFamily_infanterie,0.65)]),
            // (DamageFamily_roquette_ap, MAP [(ResistanceFamily_infanterie,0.65)]),
        ]
    DebugColor = RGBA[70, 252, 70, 255]
    DissimulationModifierGroundAir = 12
    DissimulationModifierGroundGround = 10
    HeightInMeters = 20
    InflammabilityProbability = 0.6
    Name = 'ForetLegere'
    SpeedModifierAllTerrainWheel = 0.40
    SpeedModifierInfantry = 0.75
    SpeedModifierTrack = 0.50
    ConcealmentBonus = 3
    TerrainType = ~/ETerrainType/ForetLegere
    AuthorizeNearGroundFlying = false
)
export PetitBatiment is TGameplayTerrain
(
    BloqueAmphibie = true
    BloqueAtterrissage = true
    BloqueInfanterie = true
    BloqueVehicule = true
    BloqueVision = false
    CriticalEffectProbability = 0
    DamageModifierPerFamilyAndResistance =
    MAP [
            (DamageFamily_he, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_he_autocanon, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_superhe, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_balle, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_balledca, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_howz, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_howz_bombe, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_fmballe, MAP [(ResistanceFamily_infanterie,0.5)]),
            // (DamageFamily_cac, MAP [(ResistanceFamily_infanterie,0.85)]),
            // (DamageFamily_roquette_ap, MAP [(ResistanceFamily_infanterie,0.5)]),
        ]
    DebugColor = RGBA[0, 0, 0, 255]
    DissimulationModifierGroundAir = 10.5
    DissimulationModifierGroundGround = 10
    HeightInMeters = 8
    InflammabilityProbability = 0.2
    Name = 'PetitBatiment'
    SpeedModifierAllTerrainWheel = 0
    SpeedModifierInfantry = 0.5
    SpeedModifierTrack = 0
    ConcealmentBonus = 3
    TerrainType = ~/ETerrainType/PetitBatiment
    AuthorizeNearGroundFlying = true
)
export Batiment is TGameplayTerrain
(
    BloqueAmphibie = true
    BloqueAtterrissage = true
    BloqueInfanterie = true
    BloqueVehicule = true
    BloqueVision = true
    CriticalEffectProbability = 0
    DamageModifierPerFamilyAndResistance =
    MAP [
            (DamageFamily_he, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_he_autocanon, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_superhe, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_balle, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_balledca, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_howz, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_howz_bombe, MAP [(ResistanceFamily_infanterie,0.5)]),
            (DamageFamily_fmballe, MAP [(ResistanceFamily_infanterie,0.5)]),
            // (DamageFamily_cac, MAP [(ResistanceFamily_infanterie,0.85)]),
            // (DamageFamily_roquette_ap, MAP [(ResistanceFamily_infanterie,0.5)]),
        ]
    DebugColor = RGBA[0, 0, 0, 255]
    DissimulationModifierGroundAir = 10.5
    DissimulationModifierGroundGround = 10
    HeightInMeters = 8
    InflammabilityProbability = 0.2
    Name = 'Batiment'
    SpeedModifierAllTerrainWheel = 0
    SpeedModifierInfantry = 0.5
    SpeedModifierTrack = 0
    ConcealmentBonus = 3
    TerrainType = ~/ETerrainType/Batiment
    AuthorizeNearGroundFlying = true
)
export Ruin is TGameplayTerrain
(
    BloqueAmphibie = true
    BloqueAtterrissage = true
    BloqueInfanterie = false
    BloqueVehicule = true
    BloqueVision = false
    CriticalEffectProbability = 0
    DamageModifierPerFamilyAndResistance =
    MAP [
            (DamageFamily_he, MAP [(ResistanceFamily_infanterie,0.55)]),
            (DamageFamily_he_autocanon, MAP [(ResistanceFamily_infanterie,0.55)]),
            (DamageFamily_superhe, MAP [(ResistanceFamily_infanterie,0.55)]),
            (DamageFamily_balle, MAP [(ResistanceFamily_infanterie,0.55)]),
            (DamageFamily_balledca, MAP [(ResistanceFamily_infanterie,0.55)]),
            (DamageFamily_howz, MAP [(ResistanceFamily_infanterie,0.55)]),
            (DamageFamily_howz_bombe, MAP [(ResistanceFamily_infanterie,0.55)]),
            (DamageFamily_fmballe, MAP [(ResistanceFamily_infanterie,0.55)]),
            // (DamageFamily_cac, MAP [(ResistanceFamily_infanterie,0.85)]),
            // (DamageFamily_roquette_ap, MAP [(ResistanceFamily_infanterie,0.55)]),
        ]
    DebugColor = RGBA[0, 6, 200, 255]
    DissimulationModifierGroundAir = 10.5
    DissimulationModifierGroundGround = 10
    HeightInMeters = 8
    InflammabilityProbability = 0.2
    Name = 'Ruin'
    SpeedModifierAllTerrainWheel = 0
    SpeedModifierInfantry = 0.5
    SpeedModifierTrack = 0
    ConcealmentBonus = 2.50
    TerrainType = ~/ETerrainType/Ruin
    AuthorizeNearGroundFlying = true
)

export Bloqueur is TGameplayTerrain
(
    BloqueAmphibie = true
    BloqueAtterrissage = true
    BloqueInfanterie = true
    BloqueVehicule = true
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[0, 0, 0, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0
    Name = 'Bloqueur'
    SpeedModifierAllTerrainWheel = 1
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 1
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/Bloqueur
    AuthorizeNearGroundFlying = true
)
export StrategicForest is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = false
    BloqueInfanterie = false
    BloqueVehicule = false
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[255, 255, 25, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0.1
    Name = 'StrategicForest'
    SpeedModifierAllTerrainWheel = 0.8
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 0.9
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/StrategicForest
    AuthorizeNearGroundFlying = true
)
export StrategicPlain is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = false
    BloqueInfanterie = false
    BloqueVehicule = false
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[255, 255, 25, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0.1
    Name = 'StrategicPlain'
    SpeedModifierAllTerrainWheel = 0.8
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 0.9
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/StrategicPlain
    AuthorizeNearGroundFlying = true
)
export StrategicSemiUrban is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = false
    BloqueInfanterie = false
    BloqueVehicule = false
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[255, 255, 25, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0.1
    Name = 'StrategicSemiUrban'
    SpeedModifierAllTerrainWheel = 0.8
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 0.9
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/StrategicSemiUrban
    AuthorizeNearGroundFlying = true
)
export StrategicUrban is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = false
    BloqueInfanterie = false
    BloqueVehicule = false
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[128, 128, 25, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0.1
    Name = 'StrategicUrban'
    SpeedModifierAllTerrainWheel = 0.8
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 0.9
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/StrategicUrban
    AuthorizeNearGroundFlying = true
)
export StrategicRiver is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = false
    BloqueInfanterie = false
    BloqueVehicule = false
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[25, 25, 255, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0.1
    Name = 'StrategicRiver'
    SpeedModifierAllTerrainWheel = 0.8
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 0.9
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/StrategicRiver
    AuthorizeNearGroundFlying = true
)

export EauPeuProfonde is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = true
    BloqueInfanterie = true
    BloqueVehicule = true
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[16, 228, 228, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0
    Name = 'EauPeuProfonde'
    SpeedModifierAllTerrainWheel = 1
    SpeedModifierInfantry = 0.8
    SpeedModifierTrack = 0.3
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/EauPeuProfonde
    AuthorizeNearGroundFlying = true
)

export Rocher is TGameplayTerrain
(
    BloqueAmphibie = true
    BloqueAtterrissage = true
    BloqueInfanterie = true
    BloqueVehicule = true
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[186, 247, 200, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0.1
    Name = 'Rocher'
    SpeedModifierAllTerrainWheel = 0
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 0
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/Rocher
    AuthorizeNearGroundFlying = true
)
export SmokeMedium is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = false
    BloqueInfanterie = false
    BloqueVehicule = false
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[156, 156, 156, 255]
    DissimulationModifierGroundAir = 50
    DissimulationModifierGroundGround = 50
    HeightInMeters = 25
    InflammabilityProbability = 0
    Name = 'MediumSmoke'
    SpeedModifierAllTerrainWheel = 1
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 1
    ConcealmentBonus = 4.5
    TerrainType = ~/ETerrainType/MediumSmoke
    AuthorizeNearGroundFlying = true
)
export EauProfonde is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = true
    BloqueInfanterie = true
    BloqueVehicule = true
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[16, 187, 187, 255]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0
    Name = 'EauProfonde'
    SpeedModifierAllTerrainWheel = 0.375
    SpeedModifierInfantry = 0.8
    SpeedModifierTrack = 0.375
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/EauProfonde
    AuthorizeNearGroundFlying = true
)
export BloqueConstruction is TGameplayTerrain
(
    BloqueAmphibie = false
    BloqueAtterrissage = false
    BloqueInfanterie = false
    BloqueVehicule = false
    BloqueVision = false
    CriticalEffectProbability = 0
    DebugColor = RGBA[100, 100, 100, 100]
    DissimulationModifierGroundAir = 0
    DissimulationModifierGroundGround = 0
    HeightInMeters = 0
    InflammabilityProbability = 0
    Name = 'BloqueConstruction'
    SpeedModifierAllTerrainWheel = 1
    SpeedModifierInfantry = 1
    SpeedModifierTrack = 1
    ConcealmentBonus = 1
    TerrainType = ~/ETerrainType/BloqueConstruction
    AuthorizeNearGroundFlying = true
)
