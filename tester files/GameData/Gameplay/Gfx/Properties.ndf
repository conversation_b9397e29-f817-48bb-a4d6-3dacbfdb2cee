DummyDepiction is TDepictionTemplate
(
    Scaler = TIdentityScaler()
    Selector = TConstantAlternativeSelector(Key = [LOD_High])
    DepictionAlternatives = [ DepictionDescriptor_LOD_High(MeshDescriptor = $/GFX/DepictionResources/Rien) ]
)

export InfantryDepictionSquadShowroom is TTimelyDepictionReceiverFactory
(
  DepictionTemplate = TDepictionTemplate
  (
    Selector = OnlyHighDepictionSelector
    DepictionAlternatives = [
      DepictionDescriptor_LOD_High(MeshDescriptor = $/GFX/DepictionResources/Rien),
    ]
  )
)

unnamed TMimeticGhostRegistration
(
    GfxProperties = ~/GfxProperties
    MimeticGhost = MAP
    [
        ('dummy', DummyDepiction),
    ]
)

export GfxProperties is TGfxProperties
(
    BuildingMimeticGhost = ~/BuildingMimeticGhost
    BuildingULBVDescriptor = ~/DefaultUnitLevelBuildViewDescriptor
    DistrictULBVDescriptor = ~/MapBuildingUnitLevelBuildViewDescriptor
    GhostBuildingULBVDescriptor = ~/GhostUnitLevelBuildViewDescriptor
)

DefaultRenderLayerSelector is TRenderLayerSelector(
    RenderLayerNames = [
        'Calque_Opaque',
        'Calque_Opaque_Transparency',
        'Calque_Opaque_Depiction',
        'Calque_Opaque_Depiction_SelectionMask',
    ]
)

export DepictionProperties is TDepictionProperties
(
    World = $/M3D/Scene/DefaultWorld
    DepictionMaterialPack = $/M3D/Shader/MultiRenderTypeMaterialPack_Depiction
    DistrictMaterialPack = $/M3D/Shader/MultiRenderTypeMaterialPack_District
    RenderLayerSelector = ~/DefaultRenderLayerSelector
)
