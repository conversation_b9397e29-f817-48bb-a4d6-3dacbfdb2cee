export Modele_TestUnit_ModelAvion_Strategic is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelAvion )
export Modele_TestUnit_ModelHelico_Strategic is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelHelico )
export Modele_TestUnit_ModelInfantry_Strategic is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelInfantry )
export Modele_TestUnit_ModelTank_Strategic is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelTank )
export Modele_TestUnit_Airport_Strategic is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelBuilding )

export Modele_TestUnit_ModelTank is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelTank )
export Modele_TestUnit_ModelVehicule is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelVehicule )
export Modele_TestUnit_ModelHelico is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelHelico )
export Modele_TestUnit_ModelAvion is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelAvion )
export Modele_TestUnit_ModelBateau is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelBateau )
export Modele_TestUnit_ModelInfantry is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelInfantry )
export Modele_TestUnit_ModelBuilding is TResourceMesh( Mesh=~/Private_ModelFile_TestUnit_ModelBuilding )

// TestCamo
unnamed TResourceMesh ( Mesh="CommonData:/Assets/3D/Misc/OneMeterBox.fbx" Textures="CommonData:/Assets/3D/Misc/Camo1" )
unnamed TResourceMesh ( Mesh="CommonData:/Assets/3D/Misc/OneMeterBox.fbx" Textures="CommonData:/Assets/3D/Misc/Camo2" )
unnamed TResourceMesh ( Mesh="CommonData:/Assets/3D/Misc/OneMeterBox.fbx" Textures="CommonData:/Assets/3D/Misc/Camo3" )

