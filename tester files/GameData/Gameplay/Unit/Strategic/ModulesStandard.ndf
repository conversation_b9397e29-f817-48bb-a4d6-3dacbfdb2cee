ActionPointsReachModuleDescriptor is TActionPointsReachModuleDescriptor()
IALinkToGroupModuleDescriptor is TIALinkToGroupModuleDescriptor()
StrategicSequenceModuleDescriptor is TStrategicSequenceModuleDescriptor()
StrategicAerialModuleDescriptor is TStrategicAerialModuleDescriptor()

template StrategicFatigueModuleDescriptor [ InitialFatigue ] is TStrategicFatigueModuleDescriptor
(
  MaxFatigue      = ~/StrategicMaxFatiguePerUnit
  InitialFatigue  = <InitialFatigue>
)

StrategicSelectionModuleDescriptor is TSelectionModuleDescriptor
(
    IsSelectable       = True
    IsHighlightable    = True
    HasSelectionShader = True
    HasHighlightShader = True
)

PawnPositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = True
    ClampInWorld               = False
    ClampOutMap                = False
)

StrategicStateEngineModuleDescriptor is TStateEngineUnitModuleDescriptor
(
)

StrategicIdleStatusModuleDescriptor is TStrategicIdleStatusModuleDescriptor
(
)

StrategicBuildingModuleDescriptor is TWargameBuildingModuleDescriptor
(
    BoundingBoxMinInLBU = $/GFX/Constantes/StrategicBuildingBoxMin
    BoundingBoxMaxInLBU = $/GFX/Constantes/StrategicBuildingBoxMax
)

template StrategicUIModuleDescriptor [ NameToken, ProdMenuTexture ] is TPawnUIModuleDescriptor
(
    NameToken = <NameToken>
    ProdMenuTexture = <ProdMenuTexture>
)

PawnAirplaneOrderConfigModuleDescriptor is TOrderConfigModuleDescriptor()

PawnAirportScannerConfigurationDescriptor is TScannerConfigurationDescriptor
(
    VisionRangesGRU = MAP [
        ( EVisionUnitType/Standard, 353.356890459 ),
        ( EVisionUnitType/HighAltitude, 706.713780919 ),
    ]
    OpticalStrengths = MAP [
        ( EVisionUnitType/Standard, 120.0 ),
        ( EVisionUnitType/LowAltitude, 120.0 ),
        ( EVisionUnitType/HighAltitude, 60.0 ),
    ]
)
