
export Strategic_TeamUnitDescriptor is TEntityDescriptor
(
    World = WorldIndices_Team
    DescriptorId = GUID:{********-0000-0000-0000-************}

    ModulesDescriptors =
    [
        TTeamCurrencyManagerModuleDescriptor
        (
            PiggyBanks = [
                TPiggyBankDescriptor
                (
                    CurrencyType = $/GFX/Resources/Resource_StrategicPoints
                ),
            ]
        ),
        TStrategicTeamProductionModuleDescriptor
        (
        ),
        TFactoryModuleDescriptor
        (
            Factories = [
                TVirtualFactoryDescriptor
                (
                    FactoryType = EDefaultFactories/UniversalFactory
                    IsSimultaneous = False
                ),
            ]
        ),
        TTeamDescriptionModuleDescriptor
        (
        ),
        TTeamUnitsModuleDescriptor
        (
        ),
        TTagsModuleDescriptor
        (
        ),
        TEffectApplierModuleDescriptor
        (
        ),
        TLinkAllianceModuleDescriptor
        (
        ),
        TTeamForAIModuleDescriptor
        (
        ),
        TTeamDefeatModuleDescriptor
        (
        ),
        TTeamScoreModuleDescriptor
        (
        ),
        TTeamInitialCurrencyModuleDescriptor
        (
        ),
        TFumigeneModuleDescriptor
        (
        ),
        TStatisticsModuleDescriptor
        (
        ),
        TIATeamStrategyModuleDescriptor
        (
        ),
        TStrategicStatisticModuleDescriptor
        (
        ),
    ]
)
