

Tactic_GroupModuleDescriptor is TGroupModuleDescriptor
(
)

Tactic_GroupMovementModuleDescriptor is TGroupMovementModuleDescriptor
(
)

Tactic_GroupStateEngineDescriptor is TStateEngineGroupModuleDescriptor
(
)

export Tactic_ModularUnitGroupDescriptor is TEntityDescriptor
(
    World = WorldIndices_Groups
    DescriptorId = GUID:{00000000-0000-0000-0000-000012000000}
    ClassNameForDebug = 'TacticGroup'

    ModulesDescriptors =
    [
        ~/Tactic_GroupModuleDescriptor,
        ~/Tactic_GroupMovementModuleDescriptor,
        ~/Tactic_GroupStateEngineDescriptor,
    ]
)




