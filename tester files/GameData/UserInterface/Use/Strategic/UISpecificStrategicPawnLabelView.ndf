// L'etiquette d'un pion
// -----------------------------------------------

PawnLabelMainComponentDescriptor is BUCKSensibleAreaDescriptor
(
    ElementName = 'Background'

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [40.0, 35.0]
        MagnifiableOffset = [0.0, -60.0]
        AlignementToAnchor = [0.5, 0.5]
        AlignementToFather = [0.5, 0.5]
    )

    MagnifierMultiplication = 1.3

    Components =
    [
        ~/UpperLabelList,
        ~/MainLabelList,
        ~/LowerLabelList,
    ]
)

// -----------------------------------------------

LowerPartFrameProperty is TUIFramePropertyRTTI
(
    RelativeWidthHeight = [1.0, 0.0]
    MagnifiableWidthHeight = [0.0, 25.0]
    AlignementToFather = [0.0, 1.0]
    AlignementToAnchor = [0.0, 1.0]
)

MainLabelList is TBUCKLocalLayerContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    NbLayersToLock = 3
    ChildFitToContent = true

    Components =
    [
        BUCKContainerDescriptor
        (
            ElementName = "MainLabel"

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            ChildFitToContent = true

            Components =
            [
                // Division colored background
                PanelRoundedCorner
                (
                    ElementName = "UnitTextureBackground"
                    ComponentFrame = ~/LowerPartFrameProperty
                    BorderLineColorToken = 'Transparent'
                    RoundedVertexes = [true,false,false,true]
                ),

                ~/LabelTopBar,
                ~/IconeUnit,
                ~/NoInfantryIcon,
                ~/NoCmdIcon,

                PanelRoundedCorner
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                    )

                    BorderLineColorToken = 'SM_Noir'
                    BorderThicknessToken = '2'
                    BackgroundBlockColorToken = 'Transparent'
                ),

                ~/FortifyAntiAirIcon,
                ~/CombatValue,
                ~/NoSupplied,

                PanelRoundedCorner
                (
                    ElementName = "IgnoredUntilNextTurn"
                    ComponentFrame = ~/LowerPartFrameProperty
                    BackgroundBlockColorToken = "Transparent_SM_Noir"
                    HasBorder = false
                    RoundedVertexes = [true,false,false,true]
                ),


            ]
        ),
    ]
)

// -----------------------------------------------

UpperLabelList is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 1.0]
    )

    Axis = ~/ListAxis/Vertical
    LastMargin = TRTTILength(Magnifiable = 5.0)
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'InOrderFeedback'
                ComponentFrame = TUIFramePropertyRTTI()

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                HasBackground = true
                BackgroundBlockColorToken = "SD2_InOrder"

                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent
                TextPadding = TRTTILength4(Magnifiable = [2.0, 2.0, 2.0, 2.0])

                TypefaceToken = "UIMainFont"
                BigLineAction = ~/BigLineAction/CutByDots

                TextToken = "LBS_BUSY"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextColor = "SD2_Noir"
                TextSize = "12"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'IncapacitatedFeedback'
                ComponentFrame = TUIFramePropertyRTTI()

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                HasBackground = true
                BackgroundBlockColorToken = "SD2_Routed"

                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent
                TextPadding = TRTTILength4(Magnifiable = [2.0, 2.0, 2.0, 2.0])

                TypefaceToken = "UIMainFont"
                BigLineAction = ~/BigLineAction/CutByDots

                TextToken = "LBS_OOC"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextColor = "SD2_Blanc184"
                TextSize = "12"
            )
        ),
    ]
)

// Under label List
// -----------------------------------------------

private IconeUnit is BUCKTextureDescriptor
(
    ElementName = 'UnitTexture'

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [40.0, 35.0]
    )

    TextureFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [25.0, 25.0]
        AlignementToAnchor = [0.5, 0.4]
        AlignementToFather = [0.5, 0.4]
    )
)
// -----------------------------------------------
private FortifyAntiAirIcon is BUCKTextureDescriptor
(
    ElementName = 'FortifyAntiAirIcon'
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    TextureToken = "UseStrategic_Labels_FortificationAntiAir"
    TextureFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [20.0, 20.0]
        AlignementToAnchor = [0.5, 0.5]
        AlignementToFather = [0.5, 0.5]
    )
)
// -----------------------------------------------
private CombatValue is BUCKListDescriptor
(
    ElementName = 'PowerValueGaugesContainer'

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.5, 1.0]
        AlignementToAnchor = [0.5, 1.0]
    )

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
    InterItemMargin = TRTTILength ( Magnifiable = 8.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = PowerValueContainer
            (
                ElementName = "Attack"
                BackgroundBlockColorToken = "Noir"
                TextColorToken = "BlancEquipe"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = PowerValueContainer
            (
                ElementName = "Defense"
                BackgroundBlockColorToken = "BlancEquipe"
                TextColorToken = "PureBlack"
            )
        ),
    ]

    BackgroundComponents =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToAnchor = [1.0, 0.0]
                AlignementToFather = [1.0, 0.0]
            )

            Components =
            [
                PowerValueContainer
                (
                    ElementName = "Bonus"
                    BackgroundBlockColorToken = "Noir"
                    TextColorToken = "Cyan"
                    TextToken = "FRM_PLUS"
                )
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

private Fatigue is BUCKTextureDescriptor
(
    ElementName = "FatigueIcon"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [-2.0, 5.0]
        AlignementToAnchor = [0.5, 0.5]
        AlignementToFather = [0.5, 0.5]
    )

    TextureFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )
)

//-------------------------------------------------------------------------------------

private NoSupplied is BUCKTextureDescriptor
(
    ElementName = "NoSuppliedIcon"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [25.0, 25.0]
        MagnifiableOffset = [-25.0, 5.0]
    )

    TextureToken = "UseStrategic_Labels_NoSupply"
    TextureFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
)

//-------------------------------------------------------------------------------------

private ArrowTexture is BUCKTextureDescriptor
(
    ElementName = "ArrowTexture"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [10.0, 10.0]
        AlignementToAnchor = [0.0, 0.5]
        AlignementToFather = [0.0, 0.5]
    )

    TextureToken = "UseStrategic_Labels_Arrow"
    TextureFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )
)

// -----------------------------------------------

private NoCmdIcon is BUCKTextureDescriptor
(
    ElementName = "NoCmdIcon"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [13.0, 12.0]
        MagnifiableOffset = [1.0, 10.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
    )

    TextureFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
    TextureToken = "UseStrategic_Labels_NoCmd"
)


// -----------------------------------------------

private NoInfantryIcon is BUCKTextureDescriptor
(
    ElementName = "NoInfantryIcon"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [13, 12]
        MagnifiableOffset = [1.5, 10.0]
    )

    TextureFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
    TextureToken = "UseStrategic_Labels_NoInfantry"
)
// -----------------------------------------------

private LabelTopBar is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 10.0]
    )

    Components =
    [
        // Side colored background
        PanelRoundedCorner
        (
            ElementName = "TopBarBackground"
            BackgroundBlockColorToken = "Red"
            BorderLineColorToken = "Transparent"
            RoundedVertexes = [false,true,true,false]
        ),

        ~/Fatigue,
        ~/PANumber,
    ]
)

private PANumber is BUCKContainerDescriptor
(
    ElementName = "ActionPointsContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
    )
    FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

    HasBackground = false
    BackgroundBlockColorToken = "Vert" // Placeholder color

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "ActionPoints"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Right
                VerticalAlignment = UIText_VerticalCenter
            )

            TextStyle = "Default"
            TextPadding = TRTTILength4(Magnifiable = [1.0, 0.0, 1.0, 0.0])

            HorizontalFitStyle = ~/FitStyle/FitToContent
            VerticalFitStyle = ~/FitStyle/UserDefined

            TypefaceToken = "Eurostyle_Medium"
            BigLineAction = ~/BigLineAction/ResizeFont

            TextDico = ~/LocalisationConstantes/dico_interface_ingame

            TextColor = "SD2_Blanc184"
            TextSize = "10"
        )
    ]
)

// -----------------------------------------------

template PowerValueContainer
[
    ElementName : string,
    BackgroundBlockColorToken : string,
    TextColorToken : string,
    TextToken : string = "SBO_VAL",
]
is BUCKTextDescriptor
(
    ElementName = 'PowerValueText_' + <ElementName>

    ComponentFrame = TUIFramePropertyRTTI()

    HorizontalFitStyle = ~/FitStyle/FitToContent
    VerticalFitStyle = ~/FitStyle/FitToContent

    HasBackground = true
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    HasBorder = true
    BorderThicknessToken = "1"
    BorderLineColorToken = "Noir"

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
    )

    TextPadding = TRTTILength4(Magnifiable = [2.0, 0.0, 2.0, 0.0])

    TextStyle = "Default"
    TypefaceToken = "UIMainFont"
    BigLineAction = ~/BigLineAction/ResizeFont

    TextDico = ~/LocalisationConstantes/dico_interface_ingame

    TextColor = <TextColorToken>
    TextSize = "12"

    TextToken = <TextToken>
)

// -----------------------------------------------

LowerLabelList is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.5, 1.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    FirstMargin = TRTTILength(Pixel = 4)

    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'EngagementText'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                HasBackground = true
                BackgroundBlockColorToken = "Gris_leaderboard"
                TextStyle = "Engagement"
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent
                TextPadding = TRTTILength4(Magnifiable = [2.0, 2.0, 2.0, 2.0])

                TypefaceToken = "UIMainFont"
                BigLineAction = ~/BigLineAction/CutByDots

                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                ColorMode = ~/ColorMode/Multiply

                TextColor = "Noir"
                TextSize = "12"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'FleeingFeedback'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                HasBackground = true
                BackgroundBlockColorToken = "SD2_Routed"

                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent
                TextPadding = TRTTILength4(Magnifiable = [2.0, 2.0, 2.0, 2.0])

                TypefaceToken = "UIMainFont"
                BigLineAction = ~/BigLineAction/CutByDots

                TextToken = "LBS_FLEE"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextColor = "SD2_Blanc184"
                TextSize = "12"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'UnitName'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextPadding = TRTTILength4(Magnifiable = [2.0, 2.0, 2.0, 2.0])
                TextStyle = "Default"
                HasBackground = true
                BackgroundBlockColorToken = "SD2_Gris39_avecTransparence200"
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "UIMainFont"
                BigLineAction = ~/BigLineAction/CutByDots

                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextColor = "SD2_Blanc184"
                TextSize = "12"
            )
        ),
    ]
)

// -----------------------------------------------

UISpecificStrategicPawnLabelViewDescriptor is PawnLabelViewDescriptor
(
    MainComponentDescriptor = ~/PawnLabelMainComponentDescriptor
)

// -----------------------------------------------

template PawnLabelViewDescriptor
[
    MainComponentDescriptor : TBUCKContainerDescriptor,
] is TUISpecificStrategicPawnLabelViewDescriptor
(
    MainComponentDescriptor = <MainComponentDescriptor> // Doit contenir tout les composants ! (pour des raisons d'opti)
    MainComponentContainerUniqueName = "" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    FightingPawnEngagementTextColor    = "PawnPhaseFighting"
    NonFightingPawnEngagementTextColor = "PawnPhaseNotFighting"

    EngagementTextReadyToken = "LAB_AVAI"
    EngagementTextAttackedToken = "LABEL_ATT"
    EngagementTextEngagedToken = "LABEL_ENGD"

    EngagementAnimDuration = 10 // Durée de l'animation de grossissement, en secondes
    EngagementAnimScale = 0.5 // L'échelle va varier de 1 à (1+EngagementPhaseAnimScale). Négatif autorisé
    EngagementAnimRandomStart = 0.5 // Décalage aléatoire max en secondes au démarrage.
    LabelOpacityIfNoEngagement = 0.2 // Opacité de l'étiquette des unités qui ne peuvent pas participer au combat (cf. CantFightUnitAlpha)
    LabelOpacityIfLocked = 0.4 // Opacity of locked pawn. A pawn can be locked by the scenario when it will be available in the future.

    BonusBackgroundColorTokenByTerrainType = MAP
    [
        (~/ETerrainType/StrategicForest,    "Vert"),
        (~/ETerrainType/StrategicSemiUrban, "Red"),
        (~/ETerrainType/StrategicUrban,     "Red"),
    ]
    BonusTextColorTokenByTerrainType = MAP
    [
        (~/ETerrainType/StrategicForest,    "Noir"),
        (~/ETerrainType/StrategicSemiUrban, "BlancEquipe"),
        (~/ETerrainType/StrategicUrban,     "BlancEquipe"),
    ]
    NbDigitToDisplayForBonus = 1

    FatigueTextures =
    [
        "UseStrategic_Labels_Fatigue_0",
        "UseStrategic_Labels_Fatigue_1",
        "UseStrategic_Labels_Fatigue_2",
        "UseStrategic_Labels_Fatigue_3",
        "UseStrategic_Labels_Fatigue_4",
        "UseStrategic_Labels_Fatigue_5",
        "UseStrategic_Labels_Fatigue_6",
        "UseStrategic_Labels_Fatigue_7",
        "UseStrategic_Labels_Fatigue_0",
    ]

    StrategicTerrainTypeIcons = MAP
    [
        ( ~/ETerrainType/StrategicUrban, "UseStrategic_Labels_Terrain_TerrainUrbain" ),
        ( ~/ETerrainType/StrategicSemiUrban, "UseStrategic_Labels_Terrain_TerrainUrbain" ),
        ( ~/ETerrainType/StrategicPlain, "UseStrategic_Labels_Terrain_TerrainPlaine" ),
        ( ~/ETerrainType/StrategicForest, "UseStrategic_Labels_Terrain_TerrainForet" ),
    ]

    StrategicTerrainTypeIconColors = MAP
    [
        ( ~/ETerrainType/StrategicUrban, "Gris_leaderboard" ),
        ( ~/ETerrainType/StrategicSemiUrban, "Gris_leaderboard" ),
        ( ~/ETerrainType/StrategicPlain, "Vert" ),
        ( ~/ETerrainType/StrategicForest, "Vert" ),
    ]
)
