
private EndTurnButtonSize is 64.0
private EndTurnVerticalBarOffset is 12.0

//----------------------------------------------------------------------


//----------------------------------------------------------------------

private StrategicEndTurnMainComponent is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1,0]
        MagnifiableWidthHeight = [0.0, 270.0]
        MagnifiableOffset = [0,4]
        AlignementToAnchor = [0.5, 1.0]
        AlignementToFather = [0.5, 1.0]
    )
    //HasBackground = true
    BackgroundBlockColorToken = 'SM_xanadu'
    Components =
    [
        BoutonNextTurn
    ]
)
//----------------------------------------------------------------------

SpotLight_NextTurn is BUCKSpotlightDescriptor
(
    UniqueName = "SpotLight_NextTurn"
    ElementName = 'SpotLight_NextTurn'
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.0, 0.0]
        AlignementToFather = [0.0, 0.0]
    )
)
//----------------------------------------------------------------------
private BoutonNextTurn is BUCKButtonDescriptor
(
    UniqueName = 'tuto_nextturn'
    ElementName = 'EndTurnButton'

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableOffset = [0.0, -50.0]
        AlignementToAnchor = [1.0, 1.0]
        AlignementToFather = [1.0, 1.0]
        MagnifiableWidthHeight = [0, 40.0]
    )

    LeftClickSound = SoundEvent_SteelmanNextTurn
    FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

    Components =
    [

        ~/SpotLight_NextTurn,

        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0, 1.0]
                AlignementToAnchor = [0.0, 0.5]
                AlignementToFather = [0.0, 0.5]
            )
            Axis = ~/ListAxis/Horizontal
            ChildFitToContent = true

            FirstMargin = TRTTILength(Magnifiable = 10.0)
            InterItemMargin = TRTTILength(Magnifiable = 10.0)
            LastMargin = TRTTILength(Magnifiable = 30.0)

             Elements =
             [
                 // bouton
                 BUCKListElementDescriptor
                 (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [28, 28]
                            AlignementToAnchor = [0.0, 0.5]
                            AlignementToFather = [0.0, 0.5]
                        )

                        TextureToken = "icone_nextTurn"
                        TextureColorToken = 'SM_paleSilver'
                    )
                 ),
                 // text explicatif
                 BUCKListElementDescriptor
                 (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact
                        HorizontalFitStyle = ~/FitStyle/FitToContent
                        VerticalFitStyle = ~/FitStyle/FitToContent

                        TextStyle = 'Default'
                        TypefaceToken = "Eurostyle_Heavy"

                        TextToken = 'nextT_nt'
                        TextDico = ~/LocalisationConstantes/dico_interface_ingame
                        TextSize = '20'
                        TextColor = 'SM_paleSilver'
                    )
                )
            ]
            BackgroundComponents =
            [
                PanelRoundedCorner
                (
                    BackgroundBlockColorToken = 'SM_RifleGreen_75'
                    BorderLineColorToken = 'SM_Grullo'
                )
            ]
        ),

        BUCKSpecificStrategicHintableArea
        (
            DicoToken = ~/LocalisationConstantes/dico_interface_ingame
            HintTitleToken = "nextT_nt"
            HintBodyToken = "nextT_nb"
        )
    ]
)



//---------------------------------------------------------------------
StrategicEndTurnResources is TUIStrategicEndTurnResources
(
    MainComponent = ~/StrategicEndTurnMainComponent
)

//----------------------------------------------------------------------
