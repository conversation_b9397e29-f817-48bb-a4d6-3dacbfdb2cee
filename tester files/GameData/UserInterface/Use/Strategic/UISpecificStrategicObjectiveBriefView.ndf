
BUCKSpecificStrategicObjectiveBriefMainComponentDescriptor is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
    )

    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
    FirstMargin  = TRTTILength (Magnifiable = 12.0)
    InterItemMargin = TRTTILength(Magnifiable = 10.0)
    LastMargin  = TRTTILength (Magnifiable = 4.0)

    Axis = ~/ListAxis/Vertical

    HidePointerEvents = true

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "ScoreToReachText"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent
                TextStyle = "Default"
                TypefaceToken = "Eurostyle_Medium"
                TextToken = "SC_TORCH"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextSize = "16"
                TextColor = "SM_paleSilver"
                TextPadding = TRTTILength4 (Magnifiable = [20, 0, 20, 4])
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKRackDescriptor
            (
                ElementName = "GeneralObjectiveListRack"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 0.0]
                )

                Axis = ~/ListAxis/Vertical
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                InterItemMargin = TRTTILength (Magnifiable = 6.0)

                BladeDescriptor = ~/MapGeneralObjective
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKRackDescriptor
            (
                ElementName = "OwnedObjectiveListRack"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 0.0]
                )

                Axis = ~/ListAxis/Vertical
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                InterItemMargin = TRTTILength (Magnifiable = 2.0)

                BladeDescriptor = ~/MapObjectiveWithOwner
            )
        )
    ]

    BackgroundComponents =
    [
        //OmbrePanel
        PanelRoundedCorner
        (
            RoundedVertexes = [true, false, false, false]
            BackgroundBlockColorToken = 'SM_RifleGreen_75'
            BorderLineColorToken = 'SM_Grullo'
        ),

        BUCKTextDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [-16,-44]
                MagnifiableWidthHeight = [0,40]
                AlignementToFather = [1.0, 0]
                AlignementToAnchor = [1.0, 0]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Right
                VerticalAlignment = UIText_Bottom
                InterLine = 0
            )
            TextStyle = "Default"
            TextPadding = TRTTILength4 (Magnifiable = [0,0,0,4])
            HorizontalFitStyle = ~/FitStyle/FitToContent
            VerticalFitStyle = ~/FitStyle/UserDefined
            TypefaceToken = "Eurostyle_Italic"
            BigLineAction = ~/BigLineAction/MultiLine
            TextToken = 'Obj_ti'
            TextDico = ~/LocalisationConstantes/dico_interface_ingame
            TextColor = "SM_paleSilver"
            TextSize = "12"
        )
    ]
)

MapObjectiveWithOwner is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI()
    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    FirstMargin = TRTTILength(Magnifiable = 16.0)
    LastMargin = TRTTILength(Magnifiable = 16.0)
    InterItemMargin = TRTTILength(Magnifiable = 3.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKButtonDescriptor
            (
                ElementName = "ObjectiveButton"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.0, 0.5]
                    AlignementToFather = [0.0, 0.5]
                )

                FitStyle = ~/ContainerFitStyle/FitToContent

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ElementName = "ObjectiveName"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToAnchor = [0.0, 0.5]
                            AlignementToFather = [0.0, 0.5]
                        )

                        ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact
                        HorizontalFitStyle = ~/FitStyle/FitToContent
                        VerticalFitStyle = ~/FitStyle/FitToContent
                        BigLineAction = ~/BigLineAction/MultiLine
                        TextStyle = "Default"
                        TypefaceToken = "Eurostyle_Medium"

                        TextToken = "SSC_OBJNM"
                        TextDico = ~/LocalisationConstantes/dico_interface_ingame

                        TextSize = "14"
                        TextColor = "SM_paleSilver"
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKContainerDescriptor(ComponentFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 0.0]))
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "ObjectiveBonusScore"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.0, 0.5]
                    AlignementToFather = [0.0, 0.5]
                )

                ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent
                BigLineAction = ~/BigLineAction/MultiLine
                TextStyle = "Default"
                TypefaceToken = "Eurostyle_Medium"

                TextToken = "SSC_BONUS"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextSize = "14"
                TextColor = "SM_paleSilver"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "ObjectiveIncomeScore"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.0, 0.5]
                    AlignementToFather = [0.0, 0.5]
                )

                ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent
                BigLineAction = ~/BigLineAction/MultiLine
                TextStyle = "Default"
                TypefaceToken = "Eurostyle_Medium"

                TextToken = "SSC_INCM"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextSize = "14"
                TextColor = "SM_paleSilver"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListElementDescriptor
            (
                ComponentDescriptor = BUCKTextureDescriptor
                (
                ElementName = "ObjectiveTexture"
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [23.0, 12.0]
                        AlignementToAnchor = [0.0, 0.5]
                        AlignementToFather = [0.0, 0.5]
                    )

                    TextureFrame = TUIFramePropertyRTTI()
                    ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                )
            )
        ),
    ]
)

private MapGeneralObjective is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 0.0]
    )

    FirstMargin = TRTTILength(Magnifiable = ~/OneObjectiveWidthMarginLeft)
    InterItemMargin = TRTTILength(Magnifiable = ~/OneObjectiveSpaceBetwenCheckboxAndText)
    LastMargin = TRTTILength(Magnifiable = ~/OneObjectiveWidthMarginRight)
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    Axis = ~/ListAxis/Horizontal

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI()
                Axis = ~/ListAxis/Vertical

                FirstMargin = TRTTILength(Magnifiable = 2)
                InterItemMargin = TRTTILength(Magnifiable = 2.0)
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "ObjectiveTitle"

                            ComponentFrame = TUIFramePropertyRTTI()
                            ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact

                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/FitToContent

                            TextStyle = 'Default'
                            TypefaceToken = "Eurostyle_Medium"

                            TextSize = '14'
                            TextColor = 'SM_paleSilver'
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "ObjectiveDescription"

                            ComponentFrame = TUIFramePropertyRTTI()
                            ParagraphStyle = ~/LDHintParagraphStyleLeftAlignedExact

                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/FitToContent

                            TextStyle = 'Default'
                            TypefaceToken = "Eurostyle_Italic"

                            TextSize = '14'
                            TextColor = 'SM_paleSilver'
                        )
                    ),
                ]
            )
        ),
    ]
)

UISpecificStrategicObjectiveBriefViewDescriptor is TUISpecificStrategicObjectiveBriefViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificStrategicObjectiveBriefMainComponentDescriptor
    MainComponentContainerUniqueName = "StrategicGlobalObjectivesContainer" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    CoalitionFlagTextures = MAP
    [
        (
            ECoalition/OTAN,
            "CommonTexture_MotherCountryFlag_NATO_small"
        ),
        (
            ECoalition/PACT,
            "CommonTexture_MotherCountryFlag_PACT_small"
        ),
    ]

    TextColorByObjectiveState = MAP
    [
        (
            EStateObjective/Incomplete,
            "SM_paleSilver"
        ),
        (
            EStateObjective/Failed,
            "score_05"
        ),
        (
            EStateObjective/Succes,
            "score_02"
        ),
        (
            EStateObjective/Ended,
            "Objective/Failed"
        ),
    ]
)
