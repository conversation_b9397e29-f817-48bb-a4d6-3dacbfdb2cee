//------------------------------------------------------------------------------

template PawnStatContainer
[
    TextColor : string,
    TextureToken : string,
    TextElementName : string,
    TextureSize : int,
] is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        AlignementToFather = [0.0, 0.5]
        AlignementToAnchor = [0.0, 0.5]
    )
    Axis = ~/ListAxis/Horizontal

    Elements =
    [
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI()
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [15.0, 0.0]
                )

                Components =
                [
                    BUCKTextureDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [<TextureSize>, <TextureSize>]
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )
                        TextureToken = <TextureToken>
                        TextureColorToken = <TextColor>
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    AlignementToAnchor = [0.0, 0.5]
                    AlignementToFather = [0.0, 0.5]
                )

                ElementName = <TextElementName>
                TextColor = <TextColor>

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextStyle = "Default"
                TextToken = "CatPow"

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/UserDefined

                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/CutByDots

                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextSize = "14"
            )
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI()
            )
        ),
    ]
)

//------------------------------------------------------------------------------

BUCKSpecificStrategicStartBattlePawnBriefMainComponentDescriptor is BUCKButtonDescriptor
(
    ElementName = "HeaderButton"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [80.0, 80.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    HidePointerEvents = true
    BorderThicknessToken = '1'
    BorderLineColorToken = 'Fulda2_Orange100'

    IsTogglable = true

    Components =
    [
        PanelRoundedCorner
        (
            ElementName = "FriezeContainer"
            BorderLineColorToken = 'SM_Grullo'
            BackgroundBlockColorToken = 'SM_RifleGreen'
        ),
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [60.0, 60.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            ElementName = 'NATOIcon'
            ResizeMode = ~/TextureResizeMode/UserDefined

            TextureDrawer = 'ColorMultiply_NoGrayscale'
        ),
        BUCKTextDescriptor
        (
            ElementName = 'BattalionName'
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
                MagnifiableWidthHeight = [0.0, 20.0]
                AlignementToFather = [0.0, 1.0]
                AlignementToAnchor = [0.0, 1.0]
            )
            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_Bottom
                InterLine = -0.2
            )

            TextPadding = TRTTILength4(Magnifiable = [2.0, 2.0, 2.0, 2.0])
            TextStyle = 'Default'
            TypefaceToken = "UISecondFont"

            VerticalFitStyle = ~/FitStyle/UserDefined
            BigLineAction = ~/BigLineAction/CutByDots

            TextDico = ~/LocalisationConstantes/dico_units
            TextSize = '12'
            TextColor = 'BlancEquipe'
        ),
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.3, 0.3]
                AlignementToFather = [1.0, 0.0]
                AlignementToAnchor = [1.0, 0.0]
            )

            ElementName = 'Flag'
            ResizeMode = ~/TextureResizeMode/UserDefined

            // TextureDrawer = 'ColorMultiply_NoGrayscale'
        ),
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
                MagnifiableWidthHeight = [0.0, 18.0]
                MagnifiableOffset = [0,18]
                PixelWidthHeight = [-1.0, 0.0]
                AlignementToFather = [0.5, 1.0]
                AlignementToAnchor = [0.5, 1.0]
            )

            Components =
            [
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [0.0, 1.0]
                        AlignementToFather = [0.0, 0.5]
                        AlignementToAnchor = [0.0, 0.5]
                    )
                    Axis = ~/ListAxis/Horizontal

                    Elements =
                    [
                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = PanelRoundedCorner
                            (
                                ElementName = "AttackTextPanel"
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 1.0]
                                )
                                RoundedVertexes = [true, false, false, false]
                                BorderLineColorToken = "Transparent"
                                BackgroundBlockColorToken = "Noir"

                                Components =
                                [
                                    PawnStatContainer
                                    (
                                        TextColor = "BlancEquipe"
                                        TextureToken = "UseStrategic_SelectionPanel_Attack"
                                        TextElementName = "AttackText"
                                        TextureSize = 11
                                    )
                                ]
                            )
                        ),
                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = PanelRoundedCorner
                            (
                                ElementName = "DefenseTextPanel"
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 1.0]
                                )
                                RoundedVertexes = [false, false, false, true]
                                BorderLineColorToken = "Transparent"
                                BackgroundBlockColorToken = "BlancEquipe"

                                Components =
                                [
                                    PawnStatContainer
                                    (
                                        TextColor = "Noir"
                                        TextureToken = "UseStrategic_SelectionPanel_Defense"
                                        TextElementName = "DefenseText"
                                        TextureSize = 14
                                    )
                                ]
                            )
                        ),
                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ElementName = 'BattlePlanIncapacitatedFeedback'
                                ComponentFrame = TUIFramePropertyRTTI()

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                )

                                HasBackground = true
                                BackgroundBlockColorToken = "SD2_Routed"

                                TextStyle = "Default"
                                HorizontalFitStyle = ~/FitStyle/FitToContent
                                VerticalFitStyle = ~/FitStyle/FitToContent
                                TextPadding = TRTTILength4(Magnifiable = [2.0, 2.0, 2.0, 2.0])

                                TypefaceToken = "UIMainFont"
                                BigLineAction = ~/BigLineAction/CutByDots

                                TextToken = "LBS_OOC"
                                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                                TextColor = "SD2_Blanc184"
                                TextSize = "12"
                            )
                        ),
                    ]
                )
            ]
        )
    ]
)

//------------------------------------------------------------------------------

UISpecificStrategicStartBattlePawnBriefViewDescriptor is TUISpecificStrategicStartBattlePawnBriefViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificStrategicStartBattlePawnBriefMainComponentDescriptor
    MainComponentContainerUniqueName = "" // Permet d'indiquer l'endroit ou le composant doit s'insérer
)

