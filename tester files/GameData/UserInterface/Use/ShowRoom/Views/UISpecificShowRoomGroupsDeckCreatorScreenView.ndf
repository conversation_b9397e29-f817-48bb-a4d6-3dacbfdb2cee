// Ecran de creation/edition de smart groups dans la showroom

SmartGroupUnitButtonSize is [143.0, 91.0]
CombatGroupTabWidth is 180.0

//-------------------------------------------------------------------------------------

GroupsTabManager is TBUCKRadioButtonManager()

CombatGroupsTab is BUCKButtonDescriptor
(
    ElementName = "CombatGroupsTab"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [~/CombatGroupTabWidth, 37.0]
    )

    RadioButtonManager = ~/GroupsTabManager
    IsTogglable = true
    CannotDeselect = true
    LeftClickSound = SoundEvent_SmartGroupsSwitchCompany
    HidePointerEvents = true
    MaskEvents = true

    Components =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [~/CombatGroupTabWidth, 30.0]
                MagnifiableOffset = [0.0, 1.0]
                AlignementToAnchor = [0.0, 1.0]
                AlignementToFather = [0.0, 1.0]
            )
            TextureToken = 'ShowRoomTexture_onglet_large'
        ),
        BUCKEditableTextDescriptor
        (
            ElementName = "CombatGroupsTabEditableText"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [140.0, 20.0]
                AlignementToFather  = [0.5, 1.0]
                AlignementToAnchor  = [0.5, 1.0]
            )

            ClippingContainerFrameProperty = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableWidthHeight = [-6.0, -6.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            Flags = ~/EditableTextFlag/H_CENTER | ~/EditableTextFlag/ONE_LINE

            SelectionColorToken = "DeckOverview/CaseGrisee/EditableText/Selected"
            TypefaceToken = "UIMainFont"
            TextColorToken = "noir_onlget_stats"
            TextSizeToken = "18"

            MaxChars = 30
        ),
    ]
)

//-------------------------------------------------------------------------------------

template SmartGroupInfosContainer
[
    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [-10.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    ),
    IsEditable : bool,
] is BUCKListDescriptor
(
    ElementName = "SmartGroupInfosContainer"

    ComponentFrame = <ComponentFrame>

    Axis = ~/ListAxis/Vertical

    HidePointerEvents = true

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKEditableTextDescriptor
            (
                ElementName = "SmartGroupNameEditableText"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 21.0]
                )

                ClippingContainerFrameProperty = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    MagnifiableWidthHeight = [-6.0, -6.0]
                    AlignementToFather = [0.5, 0.5]
                    AlignementToAnchor = [0.5, 0.5]
                )

                Flags = ~/EditableTextFlag/H_CENTER | ~/EditableTextFlag/ONE_LINE

                SelectionColorToken = "DeckOverview/CaseGrisee/EditableText/Selected"
                TypefaceToken = "Liberator"
                TextColorToken = "blanc_contour"
                TextSizeToken = "16"

                MaxChars = 30

                Components =
                [
                    PanelRoundedCorner
                    (
                        BackgroundBlockColorToken = "NomGroupe"
                        BorderLineColorToken = "Transparent"
                        Radius = 10
                        RoundedVertexes = [false, true, true, false] // le 1er est en bas à gauche
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/AffichageNomOfficier
        )
    ]

    ForegroundComponents = (<IsEditable> ?
        []
        : [
            BUCKButtonDescriptor
            (
                ElementName = "SmartGroupProductionButton"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                IsTogglable = true
                CannotDeselect = true
                ForceEvents = true

                MaskEvents = false
                HidePointerEvents = true
                PointerEventsToAllow = ~/EAllowablePointerEventType/Move

                HasBorder = true
                BorderLineColorToken = "BoutonVignetteAchat"
                BorderThicknessToken = "2"
            ),
        ]
    )
)

//-------------------------------------------------------------------------------------

AffichageNomOfficier is BUCKContainerDescriptor
(
    ElementName = "LeaderNameBackground"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 14.0]
    )

    HasBackground = true
    BackgroundBlockColorToken = "NomChef"

    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
                AlignementToAnchor = [0.5, 0.0]
                AlignementToFather = [0.5, 0.0]
            )

            Axis = ~/ListAxis/Horizontal
            InterItemMargin = TRTTILength(Magnifiable = 5.0)

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "SmartGroupLeaderGrade"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                        )
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Center
                            VerticalAlignment = UIText_VerticalCenter
                        )

                        TextStyle = "Default"

                        HorizontalFitStyle = ~/FitStyle/FitToContent
                        VerticalFitStyle = ~/FitStyle/UserDefined

                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/CutByDots

                        TextDico = ~/LocalisationConstantes/dico_interface_ingame

                        TextColor = "TexteNomChef"
                        TextSize = "10"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "SmartGroupLeaderName"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                        )
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Center
                            VerticalAlignment = UIText_VerticalCenter
                        )

                        TextStyle = "Default"

                        HorizontalFitStyle = ~/FitStyle/FitToContent
                        VerticalFitStyle = ~/FitStyle/UserDefined

                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/MultiLine

                        TextDico = ~/LocalisationConstantes/dico_interface_ingame

                        TextColor = "TexteNomChef"
                        TextSize = "10"
                    )
                ),
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

SmartGroupUnitSlot is BUCKButtonDescriptor
(
    ElementName = "SmartGroupUnitSlot"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [~/SmartGroupUnitButtonSize[0], ~/SmartGroupUnitButtonSize[1] - 5.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    IsTogglable = false
    IsFocusable = false

    HidePointerEvents = true

    HasBackground = true
    BackgroundBlockColorToken = "FondVide"

    HasBorder = true
    BorderThicknessToken = "1"
    BorderLineColorToken = "ContourFondVide"

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "SmartGroupUnitSlotBackground"

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/UserDefined

            TypefaceToken = "Liberator"
            BigLineAction = ~/BigLineAction/MultiLine

            TextDico = ~/LocalisationConstantes/dico_interface_ingame
            TextToken = 'CHAT_PLUS'
            TextColor = "ContourFondVide"
            TextSize = "42"
        )
    ]
)

//-------------------------------------------------------------------------------------

SmartGroupContainer is BUCKButtonDescriptor
(
    ElementName = "SmartGroupContainer"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [~/SmartGroupUnitButtonSize[0], 215.0]
    )

    FitStyle = ~/ContainerFitStyle/MaxBetweenUserDefinedAndContentVertically

    HidePointerEvents = false
    MaskEvents = false

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "SmartGroupBackground"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/UserDefined

            TypefaceToken = "Liberator"
            BigLineAction = ~/BigLineAction/MultiLine

            TextDico = ~/LocalisationConstantes/dico_interface_ingame
            TextToken = 'CHAT_PLUS'
            TextColor = "ContourFondVide"
            TextSize = "42"
        ),
        BUCKListDescriptor
        (
            ElementName = "SmartGroupMainList"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            HidePointerEvents = true
            HasBackground = false
            BackgroundBlockColorToken = "FondGroupe"

            Axis = ~/ListAxis/Vertical
            ChildFitToContent = true

            FirstMargin = TRTTILength(Magnifiable = 5.0)
            InterItemMargin = TRTTILength(Magnifiable = 0.0)
            LastMargin = TRTTILength(Magnifiable = 5.0)

            // => UnitPackComponentDescriptor pour les vignettes

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = SmartGroupInfosContainer
                    (
                        IsEditable = true
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKRackDescriptor
                    (
                        ElementName = "SmartGroupUnitRack"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 0.0]
                        )

                        Axis = ~/ListAxis/Vertical
                        InterItemMargin = TRTTILength(Magnifiable = 0.0)
                        BladeDescriptor = ~/SmartGroupUnitSlot
                    )
                ),
            ]
        ),
    ]
)

//-------------------------------------------------------------------------------------

template SmartGroupEditorMainContainer
[
    IsForChallenge : bool,
] is PanelRoundedCorner
(
    ElementName = "SmartGroupEditorMainContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [1150.0, 350.0]
        AlignementToFather = [0.0, 1.0]
        AlignementToAnchor = [0.0, 1.0]
        MagnifiableOffset = [200.0, 0.0]
    )

    BackgroundBlockColorToken = "Transparent"
    HasBorder = false

    HidePointerEvents = true

    Components =
    [
        SmartGroupsConfigurationMainPanel
        (
            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestBetweenFatherAndChildren
            MarginSize = 0
            IsForChallenge = <IsForChallenge>
        ),
        ~/BoutonAfficherMasquerSmartGroupe,
    ]
)

//-------------------------------------------------------------------------------------

template SmartGroupsConfigurationMainPanel
[
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild,
    MarginSize : float = 10.0,
    IsForChallenge : bool,
] is BUCKListDescriptor
(
    ElementName = "SmartGroupsConfigurationMainPanel"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis  = ~/ListAxis/Vertical
    BreadthComputationMode = <BreadthComputationMode>

    FirstMargin = TRTTILength(Magnifiable = 1.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKRackDescriptor
            (

                ElementName = "CombatGroupsTabRack"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 36.0]
                    MagnifiableOffset = [20.0, 0.0]
                )

                Axis = ~/ListAxis/Horizontal

                FirstMargin = TRTTILength(Magnifiable = <MarginSize>)
                LastMargin = TRTTILength(Magnifiable = <MarginSize>)

                BladeDescriptor = ~/CombatGroupsTab
            )
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = PanelRoundedCorner
            (
                ElementName = "SmartGroupsRackContainer"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    MagnifiableWidthHeight = [<MarginSize> * -2.0, 0.0]
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )

                FitStyle = ~/ContainerFitStyle/MaxBetweenUserDefinedAndContentVertically

                HasBackground = true
                BackgroundBlockColorToken = 'FondGroupe'
                HasBorder = false

                Components =
                [
                    BUCKTextureDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )
                        TextureFrame = TUIFramePropertyRTTI(MagnifiableWidthHeight = [1150.0, 1000.0])
                        ResizeMode = ~/TextureResizeMode/UserDefined
                        ClipTextureToComponent = true
                        TileTextureInComponent = true
                        TextureDrawer  = 'ColorMultiply_Additive'
                        TextureToken =  'ShowRoomTexture_foregroundEffetPapier'
                    ),
                    BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [500.0, 225.0]
                        )

                        FitStyle = ~/ContainerFitStyle/MaxBetweenUserDefinedAndContent

                        ChildFitToContent = true
                        HidePointerEvents = true

                        Components =
                        [
                            BUCKRackDescriptor
                            (
                                ElementName = "SmartGroupsRack"

                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    AlignementToFather = [0.5, 0.5]
                                    AlignementToAnchor = [0.5, 0.5]
                                )

                                Axis  = ~/ListAxis/Horizontal
                                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                                ChildFitToContent = true

                                FirstMargin = TRTTILength(Magnifiable = 5.0)
                                InterItemMargin = TRTTILength(Magnifiable = 5.0)
                                LastMargin = TRTTILength(Magnifiable = 5.0)

                                BladeDescriptor = ~/SmartGroupContainer
                            ),
                        ]
                    ),
                ] + (<IsForChallenge> ?
                [
                    ~/LaunchChallengeButton
                ] :
                [
                    BUCKContainerDescriptor
                    (
                        ElementName = "PatternSelectorContainer"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableOffset = [-20.0, -20.0]
                            AlignementToFather = [1.0, 1.0]
                            AlignementToAnchor = [1.0, 1.0]
                        )
                        FitStyle = ~/ContainerFitStyle/FitToContent

                        Components =
                        [
                            ~/PatternSelectorPanel
                        ]
                    ),
                ])
                +
                [
                    BUCKTextureDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )
                        TextureFrame   = TUIFramePropertyRTTI( MagnifiableWidthHeight = [1150.0, 1000.0] )
                        ResizeMode = ~/TextureResizeMode/UserDefined
                        ClipTextureToComponent = true
                        TileTextureInComponent = true
                        TextureDrawer  = 'ColorMultiply_Additive'
                        TextureToken =  'ShowRoomTexture_foregroundEffetPapier'
                    )
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

UnitAmountButtonRadioManager is TBUCKRadioButtonManager()

UnitAmountButton is BUCKButtonDescriptor
(
    ElementName = "UnitAmountButton"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [34.0, 20.0]
        AlignementToAnchor = [0.0, 0.5]
        AlignementToFather = [0.0, 0.5]
    )

    IsTogglable = true
    CannotDeselect = true
    RadioButtonManager = ~/UnitAmountButtonRadioManager

    HasBorder = true
    BorderLineColorToken = "BoutonTemps_Line"
    BorderThicknessToken = "1"

    HasBackground = true
    BackgroundBlockColorToken = "BoutonTemps_Background"

    LeftClickSound = ~/SoundEvent_CombatGroupUnitAmount

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "UnitAmountButtonText"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [14.0, 14.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )
            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/UserDefined

            TypefaceToken = "Liberator"
            BigLineAction = ~/BigLineAction/MultiLine

            TextDico = ~/LocalisationConstantes/dico_interface_ingame

            TextColor = "Noir_Grise"
            TextSize = "14"
        ),
    ]
)

//-------------------------------------------------------------------------------------

UnitAmountList is BUCKListDescriptor
(
    ElementName = 'UnitAmountMainList'
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [193.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    FirstMargin = TRTTILength(Magnifiable = 5.0)
    InterItemMargin = TRTTILength(Magnifiable = 5.0)
    LastMargin = TRTTILength(Magnifiable = 5.0)

    Elements =
    [
        //titre
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'UnitAmountListTitle'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [137.0, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/MultiLine

                TextToken = 'sg_am'
                TextDico = ~/LocalisationConstantes/dico_interface_outgame

                TextColor = "Gris_unitConfig_Experience"
                TextSize = "10"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKRackDescriptor
            (
                ElementName = 'UnitAmountRack'
                Axis = ~/ListAxis/Horizontal
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                FirstMargin = TRTTILength (Magnifiable = 18.0)
                InterItemMargin = TRTTILength(Magnifiable = 6.0)
                LastMargin = TRTTILength (Magnifiable = 18.0)

                BladeDescriptor = ~/UnitAmountButton
            )
        ),
    ]

    ForegroundComponents =
    [
        BUCKEditableTextDescriptor
        (
            ElementName = "UnitAmountEditableText"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
                MagnifiableWidthHeight = [0.0, 22.0]
            )

            ClippingContainerFrameProperty = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableWidthHeight = [-6.0, -6.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            Flags = ~/EditableTextFlag/H_CENTER | ~/EditableTextFlag/ONE_LINE
            SelectTextOnFocus = true

            SelectionColorToken = "DeckOverview/CaseGrisee/EditableText/Selected"
            TypefaceToken = "Liberator"
            TextColorToken = "noir_onlget_stats"
            TextSizeToken = "18"

            MaxChars = 10

            HasBackground = true
            BackgroundBlockColorToken = "White"
            HasBorder = true
            BorderThicknessToken = "1"
            BorderLineColorToken = "Fulda2_Noir"
        )
    ]
)

//-------------------------------------------------------------------------------------

PatternSelectorPanel is BUCKListDescriptor
(
    ElementName = 'InformationEditionContainer'
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [220.0, 0.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromFrameProperty
    Axis = ~/ListAxis/Vertical

    FirstMargin = TRTTILength( Magnifiable = 30.0 )
    InterItemMargin = TRTTILength( Magnifiable = 15.0 )
    LastMargin = TRTTILength( Magnifiable = 30.0 )

    Elements =
    [
        // affichage de l'image
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ElementName = "UnitPatternContainer"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [133.0, 83.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )
            )
        ),

        // UnitAmount
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ElementName = "UnitAmountContainer"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContent

                HasBorder = true
                BorderThicknessToken = "1"
                BorderLineColorToken = "MarronPanel_noir"

                Components =
                [
                    ~/UnitAmountList,
                ]
             )
        ),
        // Remove
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKButtonDescriptor
            (
                ElementName = "RemoveUnitButton"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContent

                HasBorder = true
                BorderThicknessToken = "1"
                BorderLineColorToken = "MarronPanel_noir"

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ElementName = "RemoveUnitButtonText"
                        ComponentFrame = TUIFramePropertyRTTI()
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Center
                            VerticalAlignment = UIText_VerticalCenter
                        )

                        TextStyle = "Default"

                        HorizontalFitStyle = ~/FitStyle/FitToContent
                        VerticalFitStyle = ~/FitStyle/FitToContent

                        TextPadding = TRTTILength4(Magnifiable = [4.0, 4.0, 4.0, 4.0])

                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/MultiLine

                        TextDico = ~/LocalisationConstantes/dico_interface_ingame

                        TextToken = "SGR_REMV"
                        TextColor = "Noir_Grise"
                        TextSize = "18"
                    ),
                ]
            )
        ),
    ]

    BackgroundComponents =
    [
        PanelRoundedCorner
        (
            BorderLineColorToken = 'MarronPanel_noir'
            BackgroundBlockColorToken = 'Transparent'
        ),
        BUCKTextDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [137.0, 0.0]
                AlignementToAnchor = [0.5, 0.0]
                AlignementToFather = [0.5, 0.0]
                MagnifiableOffset = [0.0, -15.0]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/FitToContent

            TypefaceToken = "Liberator"
            BigLineAction = ~/BigLineAction/MultiLine

            TextToken = 'de_sc'
            TextDico = ~/LocalisationConstantes/dico_interface_outgame

            TextColor = "Gris_unitConfig_Experience"
            TextSize = "10"
        )
    ]
)

//-------------------------------------------------------------------------------------

template AnimationPanelSmartGroupe
[
    IsForChallenge : bool,
] is BUCKTranslationAnimatedContainerDescriptor
(
    ButtonNameForTrigger = 'AnimationPanelSmartGroupe'
    FramePropertyBeforeAnimation = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    AnimationTotalDuration = 0.10

    FramePropertyAfterAnimation = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        MagnifiableOffset = [0.0, 270.0]
    )

    Components =
    [
        SmartGroupEditorMainContainer(IsForChallenge = <IsForChallenge>)
    ]
)

//-------------------------------------------------------------------------------------

BoutonAfficherMasquerSmartGroupe is BUCKButtonDescriptor
(
    ElementName = 'AnimationPanelSmartGroupe'

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [25.0, 25.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
        MagnifiableOffset = [-5.0, 45.0]
    )

    IsTogglable = true
    DefaultToggleValue = false
    RadioButtonManager =  nil
    CannotDeselect = false
    ForceEvents = false
    LeftClickSound = SoundEvent_SmartGroupsShowHidePanel

    Components =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [20.0, 20.0]
            )
            TextureToken = 'showRoomTexture_minimize'
            TextureColorToken = 'MarronPanel_noir_toggled2'
        ),
    ]
)

//-------------------------------------------------------------------------------------

DeckCategoryList is BUCKContainerDescriptor
(
    ElementName = 'DeckCategoryList'
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [~/UnitButtonMagnifiableWidth, ~/UnitButtonMagnifiableHeight]
    )

    ClipContent = true

    Components =
    [
        FactoryNameDescriptor(ElementName = 'DeckCategoryTitle'),
        NombreUniteDansLigne,
    ]
)

//-------------------------------------------------------------------------------------

private Barre_uniteSmartGroup is BUCKListDescriptor
(
    ElementName = "CategoryAndUnitList"
    ComponentFrame = TUIFramePropertyRTTI()

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    FirstMargin = TRTTILength( Magnifiable = 20.0 )
    InterItemMargin = TRTTILength( Magnifiable = 8.0 )
    LastMargin = TRTTILength( Magnifiable = 20.0 )

    Elements =
    [
        BUCKListElementDescriptor(ComponentDescriptor = ~/DeckCategoryList),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKRackDescriptor
            (
                ElementName = 'DeckCategoryFamilies'
                ComponentFrame = TUIFramePropertyRTTI()

                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
                Axis = ~/ListAxis/Horizontal

                FirstMargin = TRTTILength( Magnifiable = 1.0 )
                InterItemMargin = TRTTILength( Magnifiable = 1.0 )
                LastMargin = TRTTILength( Magnifiable = 1.0 )

                BladeDescriptor = BUCKGridDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI()

                    FirstElementMargin = TRTTILength2( Magnifiable = [5.0, 0.0] )
                    InterElementMargin = TRTTILength2( Magnifiable = [1.0, 15.0] )
                    LastElementMargin = TRTTILength2( Magnifiable = [5.0, 10.0] )

                    MaxElementsPerDimension = [12, 0]

                    // envoie UnitPackDescriptor
                )
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

// liste des unités à choisir
DeckCategoryGridScrollingContainer is BUCKSpecificScrollingContainerDescriptor
(
    ElementName = 'DeckCategoryGridScrollingContainer'
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 600.0]
        MagnifiableOffset = [0.0, 100.0]
    )

    ChildFitToContent = true
    HidePointerEvents = true
    FitStyle = ~/ContainerFitStyle/FitToContentHorizontally
    ExternalScrollbar = true
    HasVerticalScrollbar = true

    VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [8.0, 0.0]
    )

    Components =
    [
        BUCKRackDescriptor
        (
            ElementName = 'DeckCategoryRack'
            ComponentFrame = TUIFramePropertyRTTI()

            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
            Axis = ~/ListAxis/Vertical

            MagnifierMultiplication = 0.85

            BladeDescriptor = ~/Barre_uniteSmartGroup
        )
    ]
)

//-------------------------------------------------------------------------------------

UnitListAnimatedContainer is BUCKTranslationAnimatedContainerDescriptor
(
    ElementName = "UnitListAnimatedContainer"

    FramePropertyBeforeAnimation = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        AlignementToAnchor = [0.0, 1.0]
    )

    AnimationTotalDuration = 0.35
    AnimationModificator = ~/AnimModificator/CubicStep
    AnimationModificatorParameters = [0.0, 1.0]

    FramePropertyAfterAnimation = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        MagnifiableOffset = [0.0, -20.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

    Components =
    [
        ~/DeckCategoryGridScrollingContainer,
    ]
)

//-------------------------------------------------------------------------------------

ChallengeDescriptionContainer is BUCKTranslationAnimatedContainerDescriptor
(
    ElementName = "ScenarioDescriptionTopContainer"
    ButtonNameForTrigger = "BoutonAnimationScenarioContainer"

    FramePropertyBeforeAnimation = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [750.0, 0.0]
        AlignementToAnchor = [0.0, 1.0]
        AlignementToFather = [0.0, 1.0]
        MagnifiableOffset = [-1600.0, 0]
    )

    AnimationTotalDuration = 0.15

    FramePropertyAfterAnimation = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [750.0, 0.0]
        AlignementToAnchor = [0.0, 1.0]
        AlignementToFather = [0.0, 1.0]
    )

    Components =
    [
        BUCKTextureDescriptor
        (
            ElementName = "ScenarioDescriptionBackgroundTexture"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [1214.0, 806.0]
                AlignementToFather = [0.5, 1.0]
                AlignementToAnchor = [0.5, 1.0]
                MagnifiableOffset = [447.0, 30.0]
            )
            MagnifierMultiplication = 1.25

            TextureToken = "mission_dossierVide"

            Components =
            [
                BUCKButtonDescriptor
                (
                    ElementName = "BoutonAnimationScenarioContainer"

                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                        MagnifiableWidthHeight = [-30.0, 0.0]
                    )

                    IsTogglable = true
                    DefaultToggleValue = false
                    CannotDeselect = false
                    ForceEvents = false
                    HidePointerEvents = true

                    LeftClickSound = ~/SoundEvent_ShowHideUnitInfoSheet

                    Components =
                    [
                        BUCKTextureDescriptor
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                MagnifiableWidthHeight = [20.0, 20.0]
                                MagnifiableOffset =  [-25.0, 35.0]
                                AlignementToFather = [1.0, 0.0]
                                AlignementToAnchor = [1.0, 0.0]
                            )

                            TextureToken = 'showRoomTexture_minimize'
                            TextureColorToken = 'MarronPanel_noir_toggled2'

                            Rotation = 180
                        ),
                    ]
                ),
                //-------------------------------------------------------------------------------------
                // page de gauche
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                       MagnifiableWidthHeight = [526.0, 745.0]
                       MagnifiableOffset = [71.0, 31.0]
                    )

                    Components =
                    [
                        BUCKPerspectiveWarpOffscreenContainerDescriptor
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 1.0]
                            )

                            DistortTextureDrawer = $/UserInterface/UIDistortTextureDrawer
                            PointerEventsToAllow = ~/EAllowablePointerEventType/Move

                            MagnifiableTopLeftOffset = [0.0,  0.0]
                            MagnifiableTopRightOffset = [0.0, -5.0]
                            MagnifiableBottomLeftOffset = [6.0, 0.0]
                            MagnifiableBottomRightOffset = [7.0, -5.0]

                            Components =
                            [
                                BUCKContainerDescriptor
                                (
                                    ElementName = "ScenarioDescriptionContainer"
                                    ComponentFrame = TUIFramePropertyRTTI
                                    (
                                        RelativeWidthHeight = [1.0, 0.0]
                                    )

                                    FitStyle = ~/ContainerFitStyle/FitToContentVertically

                                    // envoie SummaryCampaignShowroom
                                ),
                            ]
                        ),
                    ]
                ),
                //-------------------------------------------------------------------------------------
                // page de droite
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [520.0, 741.0]
                        MagnifiableOffset = [285.0, 25.0]
                        AlignementToFather = [0.5, 0.0]
                        AlignementToAnchor = [0.5, 0.0]
                    )

                    Components =
                    [
                        BUCKPerspectiveWarpOffscreenContainerDescriptor
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 1.0]
                            )

                            DistortTextureDrawer = $/UserInterface/UIDistortTextureDrawer
                            PointerEventsToAllow = ~/EAllowablePointerEventType/Move

                            MagnifiableTopLeftOffset = [0.0, 0.0]
                            MagnifiableTopRightOffset = [5.0, 5.0]
                            MagnifiableBottomLeftOffset = [-9.0, 1.0]
                            MagnifiableBottomRightOffset = [-4.0, 10.0]

                            Components =
                            [
                                BUCKContainerDescriptor
                                (
                                    ElementName = "ScenarioMoreDescriptionContainer"
                                    ComponentFrame = TUIFramePropertyRTTI
                                    (
                                        RelativeWidthHeight = [1.0, 1.0]
                                    )

                                    // envoie ScenarioMapDescription
                                ),
                            ]
                        )
                    ]
                ),
            ]
        ),
    ]
)

//-------------------------------------------------------------------------------------

panelTitreChallenge is BUCKListDescriptor
(
    ElementName = "panelTitreChallenge"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
        MagnifiableOffset = [0.0, 40.0]
    )

    InterItemMargin = TRTTILength( Magnifiable = 10.0 )
    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )

                ParagraphStyle = paragraphStyleTextCenter
                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/MultiLine

                TextToken = 'cha_titre'
                TextDico = ~/LocalisationConstantes/dico_maps

                TextColor = "BlancTexte"
                TextSize = "30"
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

LaunchChallengeButton is ConfirmButton
(
    ElementName = "LaunchChallengeButton"
    TextToken = "CLS_LAUNCH"
    Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ENTER ) )
    ButtonMagnifiableWidthHeight = [175.0, 35.0]
    ButtonAlignementToAnchor = [1.0, 1.0]
    ButtonAlignementToFather = [1.0, 1.0]
    ButtonMagnifiableOffset = [-10.0, -10.0]
    TextTypefaceToken = "Liberator"
    TextColorToken = "tampon_vert"
    BorderLineColorToken = "tampon_vert"
    HasBackground = false
    BackgroundBlockColorToken = "loginBoutonBackground_cyan"
    TextSizeToken = "32"
    BorderThicknessToken = "3"
    BigLineAction = ~/BigLineAction/ResizeFont
    TextPadding = TRTTILength4( Magnifiable = [5.0, 0.0, 5.0, 0.0] )
)

//-------------------------------------------------------------------------------------

template BUCKSpecificShowRoomGroupsDeckCreatorScreenMainComponentDescriptor
[
    IsForChallenge : bool,
] is BUCKContainerDescriptor
(
    ElementName = "BUCKSpecificShowRoomGroupsDeckCreatorScreenMainComponent"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        DeckEditorTopBar(SelectedTab = ~/SmartGroupTab),
        AnimationPanelSmartGroupe(IsForChallenge = <IsForChallenge>),
        ~/UnitListAnimatedContainer,
    ] +
    (<IsForChallenge> ?
        [
            AnimationPanelUnitInfos(),

            ~/DeckEditorCloseButton,
            ~/ChallengeDescriptionContainer,
        ] :
        [
            AnimationPanelUnitInfos(),
            ~/DeckEditorCloseButton,
        ]
    )
)

//-------------------------------------------------------------------------------------

private SmartGroupUnitSlotRadioManager is TBUCKRadioButtonManager()

template UISpecificShowRoomGroupsDeckCreatorScreenViewDescriptor
[
    IsForChallenge : bool,
    ChallengeDescriptionType : string = "",
    ChallengeMoreDescriptionType : string = "",
] is TUISpecificShowRoomGroupsDeckCreatorScreenViewDescriptor
(
    MainComponentDescriptor = BUCKSpecificShowRoomGroupsDeckCreatorScreenMainComponentDescriptor(IsForChallenge = <IsForChallenge>)
    MainComponentContainerUniqueName = "" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    FactoryDisplayOrder =
    [
        EDefaultFactories/Logistic,
        EDefaultFactories/Infantry,
        EDefaultFactories/Art,
        EDefaultFactories/Tanks,
        EDefaultFactories/Recons,
        EDefaultFactories/DCA,
        EDefaultFactories/Helis,
        EDefaultFactories/Planes,
    ]

    UnitPackComponentDescriptor = UnitButtonPackDescriptor
    (
        IsTogglable = false
    )

    UnitPackComponentSmartGroupSlotsDescriptor = UnitButtonPackDescriptor
    (
        RadioButtonManager = ~/SmartGroupUnitSlotRadioManager
        ForceEvents = true
    )

    ReadOnlySmartGroupTabText = "HIGRPV_BTN"

    PossibleUnitAmount =
    [
        // Renseigner -1 pour un bouton qui permet d'entrer un chiffre custom
        1,
        2,
        4,
        -1,
    ]

    ShortcutsLayer = $/M3D/Input/UserInputLayerHandler/InputLayer_InGameShortcuts

    DivisionBriefDescriptor = UISpecificDivisionBriefViewDescriptor
    (
        UniqueName = "DivisionHeader_DeckCreation"
        MainComponentDescriptor = BUCKSpecificDivisionBriefMainComponentDescriptor
        (
            AlignementToAnchor = [0.5, 0.0]
            AlignementToFather = [0.5, 0.0]
            IsTogglable = false
            RadioButtonManager = nil
            MagnifierMultiplication = 0.8
            HasBattlegroup = false
            DivisionNameWidth = 400.0
        )
    )

    IncompleteUnsavedDeckWarningModale = ~/IncompleteUnsavedDeckWarningModale

    SideBySideButtonComponentDescriptor = ~/SideBySideButtonDescriptor
    AnimationButtonComponentDescriptor = ~/AnimationButtonDescriptor

    AddUnitSound = ~/SoundEvent_AddUnitToCombatGroup
    RemoveUnitSound = ~/SoundEvent_RemoveUnitFromCombatGroup

    ChallengeDescriptionType = <ChallengeDescriptionType>
    ChallengeMoreDescriptionType = <ChallengeMoreDescriptionType>
)

//-------------------------------------------------------------------------------------
