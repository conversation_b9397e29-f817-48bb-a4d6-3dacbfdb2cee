//----------------------------------------------------------------------
// Liste des LDHintComponent
//----------------------------------------------------------------------
template CutscenePanoramique
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    HidePointerEvents = false
    PointerEventsToAllow = ~/EAllowablePointerEventType/Move | ~/EAllowablePointerEventType/Button3 | ~/EAllowablePointerEventType/Scroll

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
            Components =
            [
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1,0]
                        MagnifiableWidthHeight = [0,340]
                    )

                    Components =
                    [
                        BUCKTextDescriptor
                        (
                            ElementName = 'Text1'
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1,0]
                                AlignementToAnchor = [0.5, 1.0]
                                AlignementToFather = [0.5, 1.0]

                            )

                            ParagraphStyle = TParagraphStyle
                            (
                                Alignment = UIText_Center
                                VerticalAlignment = UIText_VerticalCenter
                                BigWordAction = ~/BigWordAction/BigWordNewLine
                                InterLine = 0.5
                            )
                            TextStyle = "DefaultWithStroke_labelVille"

                            HorizontalFitStyle = ~/FitStyle/UserDefined
                            VerticalFitStyle = ~/FitStyle/FitToContent

                            TextPadding = TRTTILength4 (Magnifiable = [20,20,20,40])

                            TypefaceToken = "Eurostyle"
                            BigLineAction = ~/BigLineAction/MultiLine

                            TextColor = "Transparent"
                            TextSize = "22"

                            TextDico = ~/LocalisationConstantes/dico_dialogues
                        )
                    ]
                )
            ]
        ),
    ]
)

template LDHint_cutscene_10
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    HidePointerEvents = false
    PointerEventsToAllow = ~/EAllowablePointerEventType/Move | ~/EAllowablePointerEventType/Button3 | ~/EAllowablePointerEventType/Scroll

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 0.0]
                RelativeWidthHeight = [1.0, 1.0]
            )

            Components =
            [
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [690,0]
                        AlignementToAnchor = [1.0, 0.0]
                        AlignementToFather = [1.0, 0.0]
                        MagnifiableOffset = [0,38+5+425]
                    )

                    Axis = ~/ListAxis/Vertical

                    Elements =
                    [
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ElementName = 'Text1'
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    MagnifiableWidthHeight = [690.0, 0.0]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                    BigWordAction = ~/BigWordAction/BigWordNewLine
                                    InterLine = 0.1
                                )
                                TextStyle = "Default"

                                HorizontalFitStyle = ~/FitStyle/UserDefined
                                VerticalFitStyle = ~/FitStyle/FitToContent

                                TextPadding = TRTTILength4 (Magnifiable = [10,10,10,10])

                                TypefaceToken = "Eurostyle_Italic"
                                BigLineAction = ~/BigLineAction/MultiLine

                                TextColor = "LDHintSolo_texte_or"
                                TextSize = "18"

                                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                            )
                        ),
                    ]

                    BackgroundComponents =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes = [true, true, false, false]
                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        )
                    ]
                )
            ]
        )
    ]
)

template LDHintInfoIngameScore
[
    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [340.0, 0.0]
        MagnifiableOffset = [0.0, 150.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    ),
    FeedbackType : string = 'positive'
]
is BUCKListDescriptor
(
    ComponentFrame = <ComponentFrame>

    Axis = ~/ListAxis/Vertical
    FirstMargin = TRTTILength (Magnifiable = 40.0)
    InterItemMargin = TRTTILength (Magnifiable = 6.0)
    LastMargin = TRTTILength (Magnifiable = 12.0)
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromFrameProperty

    Elements =
    [
        // texte titre
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )
                ParagraphStyle = paragraphStyleTextCenter
                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent
                TypefaceToken = "Eurostyle_Heavy"
                BigLineAction = ~/BigLineAction/MultiLine
                TextToken = (<FeedbackType> == 'positive' ? 'Keepit' :
                                ( <FeedbackType> == 'negative' ? 'cautious' :
                                    ( <FeedbackType> == 'Steelman' ? 'nextT_nt' : 'alerte' )))

                TextColor = (<FeedbackType> == 'alert' ? "Rouge" :
                                (<FeedbackType> == 'Steelman' ? 'SM_paleSilver' : "Blanc"))

                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TextSize = "30"
            )
        ),
        //Contenu
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'Text1'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [330.0, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                    BigWordAction = ~/BigWordAction/BigWordNewLine
                    InterLine = 0.5
                )
                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent

                TextPadding = TRTTILength4 (Magnifiable = [10.0, 0.0, 10.0, 0.0])

                TypefaceToken = "Eurostyle_Medium"
                BigLineAction = ~/BigLineAction/MultiLine

                TextColor =  (<FeedbackType> == 'alert' ? "Rouge" :
                                (<FeedbackType> == 'Steelman' ? 'SM_paleSilver' : "noir_listeMission"))
                TextSize = "14"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
    ]

    ForegroundComponents =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [68.0, 68.0]
                MagnifiableOffset = [0.0, -32.0]
                AlignementToFather = [0.5, 0.0]
                AlignementToAnchor = [0.5, 0.0]
            )

            TextureToken = "LDHint_cercle_fond"
            TextureColorToken =  (<FeedbackType> == 'positive' ? 'Vert_LDHint' :
                                    ( <FeedbackType> == 'negative' ? 'Rouge_LDHint' :
                                        (<FeedbackType> == 'Steelman' ? 'SM_Feldgrau' : 'Noir' )))

            Components =
            [
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [60.0, 60.0]
                        AlignementToFather = [0.5, 0.5]
                        AlignementToAnchor = [0.5, 0.5]
                    )

                    TextureToken = (<FeedbackType> == 'alert' ? 'LDHintCampaign_InfoTexture' :
                                    (<FeedbackType> == 'Steelman' ? 'icone_nextTurn' :
                                         'icone_scorePoints' ))
                    TextureColorToken = (<FeedbackType> == 'Steelman' ? 'SM_paleSilver':  'Blanc')
                )
            ]
        ),
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [68.0, 68.0]
                MagnifiableOffset = [0.0, -32.0]
                AlignementToFather = [0.5, 0.0]
                AlignementToAnchor = [0.5, 0.0]
            )

            TextureToken = "LDHint_cercle_contour"
            TextureColorToken = (<FeedbackType> == 'alert' ? 'Rouge' :
                                    (<FeedbackType> == 'Steelman' ? 'SM_paleSilver' : "Blanc"))
        ),
    ]

    BackgroundComponents =
    [
        PanelRoundedCorner
        (
            BackgroundBlockColorToken = (<FeedbackType> =='positive' ? 'Vert_LDHint' :
                                            (<FeedbackType> == 'negative' ? 'Rouge_LDHint' :
                                                (<FeedbackType> == 'Steelman' ? 'SM_Feldgrau' : "Noir")))
            BorderLineColorToken = (<FeedbackType> == 'alert' ? 'Rouge' :
                                    (<FeedbackType> == 'Steelman' ? 'SM_paleSilver' : "BlancTexte"))
            BorderThicknessToken = '2'
            Radius = 10
        )
    ]
)

template Component_1Text_hint
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 0.0]
                RelativeWidthHeight = [1.0, 0.0]

                AlignementToFather = [0.5, 1.0]
                AlignementToAnchor = [0.5, 1.0]

                MagnifiableOffset = [0.0, -200.0]
            )

            Components =
            [
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [900.0, 0.0]
                        AlignementToFather = [0.5, 1]
                        AlignementToAnchor = [0.5, 1]
                        MagnifiableOffset =  [0,0]
                    )

                    Axis = ~/ListAxis/Vertical

                    Elements =
                    [
                        //Separateur du haut
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),

                        //-------------------------------------------------------------------------------------
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ElementName = 'Text1'
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 0.0]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                    BigWordAction = ~/BigWordAction/BigWordNewLine
                                    InterLine = 0.5
                                )
                                TextStyle = "Default"

                                HorizontalFitStyle = ~/FitStyle/UserDefined
                                VerticalFitStyle = ~/FitStyle/FitToContent

                                TextPadding = TRTTILength4 (Magnifiable = [10,0,10,0])

                                TypefaceToken = "Eurostyle"
                                BigLineAction = ~/BigLineAction/MultiLine

                                TextColor = "LDHintSolo_texte"
                                TextSize = "22"

                                TextDico = ~/LocalisationConstantes/dico_dialogues
                            )
                        ),

                        //-------------------------------------------------------------------------------------

                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = LDHintSeparatorBetweenContentAndButtons()
                        ),

                        //Boutons
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintDefaultButtonList
                            (
                                InterItemMarginAsFloat = ~/LDHintMagnifiableInterButtonMargin
                                BladeDescriptor = LDHintBlueButton()
                            )
                        ),

                        //Separateur du bas
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),
                    ]

                    BackgroundComponents =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes = [true, true, true, true]

                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        )
                    ]
                )
            ]
        ),
    ]
)

template Component_1Text
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 0.0]
                RelativeWidthHeight = [1.0, 0.0]

                AlignementToFather = [0.5,0]
                AlignementToAnchor = [0.5,0]

                MagnifiableOffset = [0,43]
            )

            Components =
            [
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [900.0, 0.0]
                        AlignementToFather = [0.5, 0]
                        AlignementToAnchor = [0.5, 0]
                    )

                    Axis = ~/ListAxis/Vertical

                    Elements =
                    [
                        //Separateur du haut
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),

                        //-------------------------------------------------------------------------------------
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ElementName = 'Text1'
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 0.0]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                    BigWordAction = ~/BigWordAction/BigWordNewLine
                                    InterLine = 0.5
                                )
                                TextStyle = "Default"

                                HorizontalFitStyle = ~/FitStyle/UserDefined
                                VerticalFitStyle = ~/FitStyle/FitToContent

                                TextPadding = TRTTILength4 (Magnifiable = [10,0,10,0])

                                TypefaceToken = "Eurostyle_Italic"
                                BigLineAction = ~/BigLineAction/MultiLine

                                TextColor = "LDHintSolo_texte_or"
                                TextSize = "22"

                                TextDico = ~/LocalisationConstantes/dico_dialogues
                            )
                        ),

                        //-------------------------------------------------------------------------------------

                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = LDHintSeparatorBetweenContentAndButtons()
                        ),

                        //Boutons
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintDefaultButtonList
                            (
                                InterItemMarginAsFloat = ~/LDHintMagnifiableInterButtonMargin
                                BladeDescriptor = LDHintBlueButton()
                            )
                        ),

                        //Separateur du bas
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),
                    ]

                    BackgroundComponents =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes =[true, true, true, true]

                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        )
                    ]
                )
            ]
        ),
    ]
)

template Component_1Text_FullScreen
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )
    HidePointerEvents = true
    HasBackground = true
    BackgroundBlockColorToken = 'Noir'

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = 'Text1'
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
                BigWordAction = ~/BigWordAction/BigWordNewLine
                InterLine = 0.5
            )
            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/FitToContent
            VerticalFitStyle = ~/FitStyle/FitToContent

            TypefaceToken = "Eurostyle"
            BigLineAction = ~/BigLineAction/MultiLine

            TextColor = "Blanc"
            TextSize = "65"

            TextDico = ~/LocalisationConstantes/dico_dialogues
        )
    ]
)

template Component_1Texture_1Text
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    HidePointerEvents = true
    PointerEventsToAllow = ~/EAllowablePointerEventType/Move | ~/EAllowablePointerEventType/Button3 | ~/EAllowablePointerEventType/Scroll

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 0.0]
                RelativeWidthHeight = [1.0, 0.0]

                AlignementToFather = [0.5, 1.0]
                AlignementToAnchor = [0.5, 1.0]

                MagnifiableOffset = [0,-250]
            )

            Components =
            [
                BUCKTextureDescriptor
                (
                    ElementName = 'Texture1'
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                         MagnifiableWidthHeight = [660.0, 409.0]
                         AlignementToAnchor = [0.5, 1.0]
                         AlignementToFather = [0.5, 1.0]
                    )

                    TextureFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                    )
                ),
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [1280.0, 0.0]
                        AlignementToFather = [0.5,0]
                        AlignementToAnchor = [0.5,0]
                    )

                    Axis = ~/ListAxis/Vertical

                    Elements =
                    [
                        //Separateur du haut
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),

                        //-------------------------------------------------------------------------------------
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ElementName = 'Text1'
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    MagnifiableWidthHeight = [1280.0, 0.0]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                    BigWordAction = ~/BigWordAction/BigWordNewLine
                                    InterLine = 0.5
                                )
                                TextStyle = "Default"

                                HorizontalFitStyle = ~/FitStyle/UserDefined
                                VerticalFitStyle = ~/FitStyle/FitToContent

                                TextPadding = TRTTILength4 (Magnifiable = [10,0,10,0])

                                TypefaceToken = "Eurostyle"
                                BigLineAction = ~/BigLineAction/MultiLine

                                TextColor = "LDHintSolo_texte"
                                TextSize = "22"

                                TextDico = ~/LocalisationConstantes/dico_dialogues
                            )
                        ),

                        //-------------------------------------------------------------------------------------

                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = LDHintSeparatorBetweenContentAndButtons()
                        ),

                        //Boutons
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintDefaultButtonList
                            (
                                InterItemMarginAsFloat = ~/LDHintMagnifiableInterButtonMargin
                                BladeDescriptor = LDHintBlueButton()
                            )
                        ),

                        //Separateur du bas
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),
                    ]

                    BackgroundComponents =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes = [true, true, true, true]

                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        )
                    ]
                )
            ]
        ),
    ]
)

template Component_1Texture_1Text_Portrait
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 0.0]
                RelativeWidthHeight = [1.0, 0.0]

                AlignementToFather = [0.5,0]
                AlignementToAnchor = [0.5,0]

                MagnifiableOffset = [0,70]
            )

            Components =
            [
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [310*0.75,310*0.75]
                        AlignementToFather = [0.5, 0.0]
                        MagnifiableOffset = [450.0, 0.0]
                    )

                    Components =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes = [false, false, true, true]
                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        ),
                        BUCKTextureDescriptor
                        (
                            ElementName = 'Texture1'
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                 MagnifiableWidthHeight = [225.0, 225.0]
                                 AlignementToAnchor = [0.5, 0.5]
                                 AlignementToFather = [0.5, 0.5]
                            )

                            TextureFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 1.0]
                            )
                        )
                    ]
                ),
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [900.0, 0.0]
                        AlignementToFather = [0.5,0]
                        AlignementToAnchor = [0.5,0]
                    )

                    Axis = ~/ListAxis/Vertical

                    Elements =
                    [
                        //Separateur du haut
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),

                        //-------------------------------------------------------------------------------------
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ElementName = 'Text1'
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 0.0]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                    BigWordAction = ~/BigWordAction/BigWordNewLine
                                    InterLine = 0.5
                                )
                                TextStyle = "Default"

                                HorizontalFitStyle = ~/FitStyle/UserDefined
                                VerticalFitStyle = ~/FitStyle/FitToContent

                                TextPadding = TRTTILength4 (Magnifiable = [10,0,10,0])

                                TypefaceToken = "Eurostyle_Italic"
                                BigLineAction = ~/BigLineAction/MultiLine

                                TextColor = "LDHintSolo_texte_or"
                                TextSize = "22"

                                TextDico = ~/LocalisationConstantes/dico_dialogues
                            )
                        ),

                        //-------------------------------------------------------------------------------------

                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = LDHintSeparatorBetweenContentAndButtons()
                        ),

                        //Boutons
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintDefaultButtonList
                            (
                                InterItemMarginAsFloat = ~/LDHintMagnifiableInterButtonMargin
                                BladeDescriptor = LDHintBlueButton()
                            )
                        ),

                        //Separateur du bas
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),
                    ]

                    BackgroundComponents =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes = [true, true , false, false]

                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        )
                    ]
                )
            ]
        ),
    ]
)

template Component_1Texture_1Text_TextureLeft
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 0.0]
                RelativeWidthHeight = [1.0, 0.0]

                AlignementToFather = [0.5, 1.0]
                AlignementToAnchor = [0.5, 1.0]

                MagnifiableOffset = [0.0, -200.0]
            )

            Components =
            [
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [160.0, 160.0]
                        AlignementToFather = [0.5, 1.0]
                        AlignementToAnchor = [1.0, 1.0]
                        MagnifiableOffset = [-450.0, 0.0]
                    )

                    Components =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes = [true, true, false, false]
                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        ),
                        BUCKTextureDescriptor
                        (
                            ElementName = 'Texture1'
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                 MagnifiableWidthHeight = [150.0, 150.0]
                                 AlignementToAnchor = [0.5, 0.5]
                                 AlignementToFather = [0.5, 0.5]
                            )

                            TextureFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 1.0]
                            )
                        )
                    ]
                ),
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [900.0, 0.0]
                        AlignementToFather = [0.5, 1]
                        AlignementToAnchor = [0.5, 1]
                    )

                    Axis = ~/ListAxis/Vertical

                    Elements =
                    [
                        //Separateur du haut
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),

                        //-------------------------------------------------------------------------------------
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ElementName = 'Text1'
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 0.0]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                    BigWordAction = ~/BigWordAction/BigWordNewLine
                                    InterLine = 0.5
                                )
                                TextStyle = "Default"

                                HorizontalFitStyle = ~/FitStyle/UserDefined
                                VerticalFitStyle = ~/FitStyle/FitToContent

                                TextPadding = TRTTILength4 (Magnifiable = [10,0,10,0])

                                TypefaceToken = "Eurostyle"
                                BigLineAction = ~/BigLineAction/MultiLine

                                TextColor = "LDHintSolo_texte"
                                TextSize = "22"

                                TextDico = ~/LocalisationConstantes/dico_dialogues
                            )
                        ),

                        //-------------------------------------------------------------------------------------

                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = LDHintSeparatorBetweenContentAndButtons()
                        ),

                        //Boutons
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintDefaultButtonList
                            (
                                InterItemMarginAsFloat = ~/LDHintMagnifiableInterButtonMargin
                                BladeDescriptor = LDHintBlueButton()
                            )
                        ),

                        //Separateur du bas
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),
                    ]

                    BackgroundComponents =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes = [false, false, true, true]

                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        )
                    ]
                )
            ]
        ),
    ]
)

template Component_Operation_1Texture_1Text
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 0.0]
                RelativeWidthHeight = [1.0, 0.0]

                AlignementToFather = [0.0, 0.5]
                AlignementToAnchor = [0.0, 0.5]

                MagnifiableOffset = [10.0, 0.0]
            )

            Components =
            [

                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [450.0, 0.0]
                        AlignementToFather = [0.0, 0.5]
                        AlignementToAnchor = [0.0, 0.5]
                    )

                    Axis = ~/ListAxis/Vertical

                    Elements =
                    [
                        //Separateur du haut
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextureDescriptor
                            (
                                ElementName = 'Texture1'
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                     MagnifiableWidthHeight = [440.0*0.8, 359.0*0.8]
                                )

                                TextureFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 1.0]
                                )
                            )
                        ),

                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKListDescriptor

                            (
                            Axis = ~/ListAxis/Vertical
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                MagnifiableWidthHeight = [450.0, 0.0]

                            )

                            Elements =
                            [
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = LDHintTopBottomBorder()
                                ),

                                //-------------------------------------------------------------------------------------
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = BUCKTextDescriptor
                                    (
                                        ElementName = 'Text1'
                                        ComponentFrame = TUIFramePropertyRTTI
                                        (
                                            RelativeWidthHeight = [1.0, 0.0]
                                        )

                                        ParagraphStyle = TParagraphStyle
                                        (
                                            Alignment = UIText_Center
                                            VerticalAlignment = UIText_VerticalCenter
                                            BigWordAction = ~/BigWordAction/BigWordNewLine
                                            InterLine = 0.5
                                        )
                                        TextStyle = "Default"

                                        HorizontalFitStyle = ~/FitStyle/UserDefined
                                        VerticalFitStyle = ~/FitStyle/FitToContent

                                        TextPadding = TRTTILength4 (Magnifiable = [10,0,10,0])

                                        TypefaceToken = "Eurostyle"
                                        BigLineAction = ~/BigLineAction/MultiLine

                                        TextColor = "LDHintSolo_texte"
                                        TextSize = "20"

                                        TextDico = ~/LocalisationConstantes/dico_dialogues

                                    )
                                ),

                                //Boutons
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = LDHintDefaultButtonList
                                    (
                                        InterItemMarginAsFloat = ~/LDHintMagnifiableInterButtonMargin
                                        BladeDescriptor = LDHintBlueButton()
                                    )
                                ),

                                //Separateur du bas
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = LDHintTopBottomBorder()
                                ),
                            ]
                            BackgroundComponents =
                            [
                                PanelRoundedCorner
                                (
                                    Radius = 5
                                    BackgroundBlockColorToken = 'LDHintSolo_fond'
                                    BorderLineColorToken = 'LDHintSolo_texte'
                                    BorderThicknessToken = '2'
                                )
                            ]
                            )
                        ),


                    ]


                )
            ]
        ),
    ]
)
template Component_1Video_1Text
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    HidePointerEvents = false

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [0.0, 0.0]
                RelativeWidthHeight = [1.0, 0.0]

                AlignementToFather = [0.5,1.0]
                AlignementToAnchor = [0.5,1.0]

                MagnifiableOffset = [0.0,-200.0]
            )

            Components =
            [
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [310.0, 310.0]
                        AlignementToFather = [0.5, 1.0]
                        AlignementToAnchor = [1.0, 1.0]
                        MagnifiableOffset = [-450.0, 0.0]
                    )

                    Components =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes = [true, true, false, false]
                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        ),
                        BUCKVideoDescriptor
                        (
                            ElementName = "Video1"

                            ComponentFrame = TUIFramePropertyRTTI
                            (
                               MagnifiableWidthHeight = [300.0, 300.0]
                               AlignementToAnchor = [0.5, 0.5]
                               AlignementToFather = [0.5, 0.5]
                            )

                            VideoDrawer  = $/UserInterface/UIVideoDrawer
                            VideoFrame   = TUIFramePropertyRTTI( RelativeWidthHeight = [1.,1.])
                        )
                    ]
                ),
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [900.0, 0.0]
                        AlignementToFather = [0.5, 1]
                        AlignementToAnchor = [0.5, 1]
                    )

                    Axis = ~/ListAxis/Vertical

                    Elements =
                    [
                        //Separateur du haut
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),

                        //-------------------------------------------------------------------------------------
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ElementName = 'Text1'
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    MagnifiableWidthHeight = [900.0, 0.0]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                    BigWordAction = ~/BigWordAction/BigWordNewLine
                                    InterLine = 0.5
                                )
                                TextStyle = "Default"

                                HorizontalFitStyle = ~/FitStyle/UserDefined
                                VerticalFitStyle = ~/FitStyle/FitToContent

                                TextPadding = TRTTILength4 (Magnifiable = [10,0,10,0])

                                TypefaceToken = "Eurostyle"
                                BigLineAction = ~/BigLineAction/MultiLine

                                TextColor = "LDHintSolo_texte"
                                TextSize = "22"

                                TextDico = ~/LocalisationConstantes/dico_dialogues
                            )
                        ),

                        BUCKListElementDescriptor
                        (
                            ExtendWeight = 1.0
                            ComponentDescriptor = LDHintSeparatorBetweenContentAndButtons()
                        ),

                        //Boutons
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintDefaultButtonList
                            (
                                InterItemMarginAsFloat = ~/LDHintMagnifiableInterButtonMargin
                                BladeDescriptor = LDHintBlueButton()
                            )
                        ),

                        //Separateur du bas
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = LDHintTopBottomBorder()
                        ),
                    ]

                    BackgroundComponents =
                    [
                        PanelRoundedCorner
                        (
                            RoundedVertexes = [false, false, true, true]
                            Radius = 5
                            BackgroundBlockColorToken = 'LDHintSolo_fond'
                            BorderLineColorToken = 'LDHintSolo_texte'
                            BorderThicknessToken = '2'
                        )
                    ]
                )
            ]
        ),
    ]
)

//----------------------------------------------------------------------------------
//template pour diapo
//----------------------------------------------------------------------------------
// MiseEnScene
//-------------------------------------------------------------------------------------
template Component_1_Texture_1Text_FullScreen
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )
    HidePointerEvents = true
    HasBackground = true
    BackgroundBlockColorToken = 'Noir'
    ImageSize is [800, 1038]

    Components =
    [
        BUCKTranslationAnimatedContainerDescriptor
        (
            FramePropertyBeforeAnimation = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [ImageSize[0]*5,ImageSize[1]*5]
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]
            )

            TriggerWhenSetVisible = true
            AnimationTotalDuration = 4

            FramePropertyAfterAnimation = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = ImageSize
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]
            )

            Components =
            [
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1,1]
                        AlignementToAnchor = [0.5, 0.5]
                        AlignementToFather = [0.5, 0.5]
                    )

                    Components =
                    [
                        BUCKTextureDescriptor
                        (
                            ElementName = 'Texture1'
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1,1]
                            )
                        )
                    ]
                )
            ]
        ),

        BUCKTextDescriptor
        (
            ElementName = 'Text1'
            ComponentFrame = TUIFramePropertyRTTI
            (

                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
                BigWordAction = ~/BigWordAction/BigWordNewLine
                InterLine = 0.5
            )
            TextStyle = "Default"

            HorizontalFitStyle = ~/FitStyle/FitToContent
            VerticalFitStyle = ~/FitStyle/FitToContent

            TypefaceToken = "Eurostyle"
            BigLineAction = ~/BigLineAction/MultiLine

            TextColor = "Blanc"
            TextSize = "65"

            TextDico = ~/LocalisationConstantes/dico_dialogues
        )
    ]
)
//-------------------------------------------------------------------------------------

template TitreChallenge
[
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    HasBackground = true
    BackgroundBlockColorToken = 'Noir30'

    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [494,0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            LastMargin = TRTTILength( Magnifiable = 0.0 )
            Axis = ~/ListAxis/Vertical

            HidePointerEvents = true

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1,0]
                            MagnifiableWidthHeight = [0,40]
                        )

                        HasBackground = true
                        BackgroundBlockColorToken = 'challenge_titre'

                        HasBorder = true
                        BorderThicknessToken = '1'
                        BorderLineColorToken = 'challenge_bordure'
                        BordersToDraw = ~/TBorderSide/Top |~/TBorderSide/Left |~/TBorderSide/Right

                        ParagraphStyle = paragraphStyleTextCenter
                        TextStyle = "Default"
                        HorizontalFitStyle = ~/FitStyle/UserDefined
                        VerticalFitStyle = ~/FitStyle/UserDefined
                        TypefaceToken = "Eurostyle_Heavy"
                        BigLineAction = ~/BigLineAction/MultiLine
                        TextToken = 'intel'
                        TextDico = ~/LocalisationConstantes/dico_interface_ingame
                        TextColor = "BlancEquipe"
                        TextSize = "18"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = 'Texture1'
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [494.0, 558.0]
                            AlignementToAnchor = [0.0, 0.0]
                            AlignementToFather = [0.0, 0.0]
                        )

                        HasBorder = true
                        BorderThicknessToken = '1'
                        BorderLineColorToken = 'challenge_bordure'
                        BordersToDraw = ~/TBorderSide/Left | ~/TBorderSide/Right
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = 'Text1'
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1,0]
                        )

                        HasBackground = true
                        BackgroundBlockColorToken = 'Noir'

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_Up
                            BigWordAction = ~/BigWordAction/BigWordNewLine
                            InterLine = 0.5
                        )
                        TextStyle = "Default"
                        TextPadding = TRTTILength4 ( Magnifiable = [20,10,20,10])
                        HorizontalFitStyle = ~/FitStyle/UserDefined
                        VerticalFitStyle = ~/FitStyle/FitToContent

                        TypefaceToken = "Eurostyle"
                        BigLineAction = ~/BigLineAction/MultiLine

                        TextColor = "Blanc"
                        TextSize = "13"

                        TextDico = ~/LocalisationConstantes/dico_dialogues
                    )
                ),
                //Boutons
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1,0]
                            MagnifiableWidthHeight = [0,84]
                        )

                        HasBackground = true
                        BackgroundBlockColorToken = 'challenge_fond'

                        HasBorder = true
                        BorderThicknessToken = '1'
                        BorderLineColorToken = 'challenge_bordure'
                        BordersToDraw = ~/TBorderSide/Bottom |~/TBorderSide/Left |~/TBorderSide/Right

                        Components =
                        [
                            LDHintDefaultButtonList
                            (
                                InterItemMarginAsFloat = ~/LDHintMagnifiableInterButtonMargin
                                BladeDescriptor = LDHintBlueButton()
                            )
                        ]
                    )
                )
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------
template SuperHint
[
    alignementGauche : boolean = true,
    challenge : boolean = false,
]
 is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableOffset = (<challenge> ? [-335.0, 43.0] : (<alignementGauche> ? [10.0, 43.0] : [-10.0, 43.0]))
        MagnifiableWidthHeight = (<challenge> ? [387.0, 0.0] : [360.0, 0.0])
        AlignementToAnchor = (<alignementGauche> ? [0.0, 0.0] : [1.0, 0.0] )
        AlignementToFather = (<alignementGauche> ? [0.0, 0.0] : [1.0, 0.0] )
    )

    FirstMargin = TRTTILength ( Magnifiable = 10.0 )
    InterItemMargin = TRTTILength( Magnifiable = 10.0 )
    LastMargin = TRTTILength( Magnifiable = 10.0 )
    Axis = ~/ListAxis/Vertical
    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [-40.0, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                    BigWordAction = ~/BigWordAction/BigWordNewLine
                    InterLine = 0.5
                )

                TextStyle = "Default"
                TextToken = 'OBJ_NEWI'
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Eurostyle_Medium"
                BigLineAction = ~/BigLineAction/MultiLine

                TextColor = "ObjectiveLabel/Primary/Title"
                TextSize = "20"

                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'Text1'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [-50,0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                    BigWordAction = ~/BigWordAction/BigWordNewLine
                    InterLine = 0.5
                )
                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Eurostyle"
                BigLineAction = ~/BigLineAction/MultiLine

                TextColor = "BlancEquipe"
                TextSize = "14"

                TextDico = ~/LocalisationConstantes/dico_dialogues
            )
        )
    ]

    BackgroundComponents =
    [
        PanelRoundedCorner()
    ]
)
