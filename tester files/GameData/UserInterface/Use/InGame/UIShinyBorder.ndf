ShinyBorder is AuraDescriptor
(
    //TextureDrawer = "ColorMultiply"
    AuraBorderSize = 6.0
    TopTextureToken = "CommonTexture_Spotlight_TopTileTexture"
    BottomTextureToken = "CommonTexture_Spotlight_BottomTileTexture"
    LeftTextureToken = "CommonTexture_Spotlight_LeftTileTexture"
    RightTextureToken = "CommonTexture_Spotlight_RightTileTexture"
    TopLeftTexture = "CommonTexture_Spotlight_CornerTexture_TopLeft"
    TopRightTexture = "CommonTexture_Spotlight_CornerTexture_TopRight"
    BottomRightTexture = "CommonTexture_Spotlight_CornerTexture_BottomRight"
    BottomLeftTexture = "CommonTexture_Spotlight_CornerTexture_BottomLeft"
)