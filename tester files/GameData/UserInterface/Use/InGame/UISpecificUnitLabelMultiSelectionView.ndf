MultiSelectionUpperNameAndUnitCountDescription is TNameAndUnitCountDescription
(
    TextFormatScript = ~/DefaultTextFormatScript

    // All Texts
    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0
        OpticalAlignment = true
    )

    TypefaceToken = "UISecondFont"
    UnitInfoTextSize = "14"

    // Unit Name
    UnitNameTextStyle = "Default"
    HideBackground = true

    // Unit Number
    NbUnitsBackgroundColor = "SD2_Gris80"
    NbUnitsTextStyle = "LabelUnitNameStroke"
    NbUnitsTextColor = "Blanc"

    UnitInfoVPadding = TRTTILength2(Magnifiable = [0.0, 0.0])
    UnitNameHPadding = TRTTILength2(Magnifiable = [1.0, 1.0])
    UnitNumberHPadding = TRTTILength2(Magnifiable = [1.0, 1.0])
)

MultiSelectionCarriedNameAndUnitCountDescription is TNameAndUnitCountDescription
(
    TextFormatScript = ~/DefaultTextFormatScript

    // All Texts
    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0
        OpticalAlignment = true
    )

    TypefaceToken = "UISecondFont"
    UnitInfoTextSize = "10"

    // Unit Name
    UnitNameTextStyle = "Default"
    HideBackground = true

    // Unit Number
    NbUnitsBackgroundColor = "SD2_Gris80"
    NbUnitsTextStyle = "LabelUnitNameStroke"
    NbUnitsTextColor = "Blanc"

    UnitInfoVPadding = TRTTILength2(Magnifiable = [1.0, 1.0])
    UnitNameHPadding = TRTTILength2(Magnifiable = [3.0, 3.0])
    UnitNumberHPadding = TRTTILength2(Magnifiable = [1.0, 1.0])
)

//-------------------------------------------------------------------------------------
private MultiSelectionUnitIcon is TBUCKSpecificLabelUnitIconDescriptor
(
    ElementName = "UnitIcon"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [34.0, 34.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    ChildFitToContent = true
    LocalRenderLayer = 2

    TextureDrawer = "ColorMultiply"
    UniformDrawer = $/UserInterface/UIUniformDrawer

    HasBorder = false
    BorderThicknessToken = "2"
    BorderLineColorToken = "Blanc"
    HasBackground = false

    MoraleAndHPGaugesDescription = ~/MoraleAndHPGaugesDescription
    SmartChipDescription = ~/SmartChipDescription
)
//-------------------------------------------------------------------------------------
MultiSelectionUnitLabelUpperList is TBUCKSpecificLabelUpperListDescriptor
(
    ElementName = "LabelUpperList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    NameAndUnitCountDescription = ~/MultiSelectionUpperNameAndUnitCountDescription

    IsClippable = true
    HasBackground = false
    ChildFitToContent = false

    BackgroundLocalRenderLayer = 2
    LocalRenderLayer = 3
    UniformDrawer = $/UserInterface/UIUniformDrawer
    TextureDrawer = "ColorMultiply"

    // Evacuation Icon
    EvacuationIconTextureName = "icone_evacuation"
    EvacuationIconMagnifiableSize = 23.0

    // Reload Icon
    ReloadIconMagnifiableSize = 22.0
    ReloadIconDrawer = $/UserInterface/ChronoDrawerCommon
    ReloadIconChronoTexture = "icone_reticule"
    ReloadIconTextureToken = "icone_reticule_vide"
    ReloadIconChronoBackgroundColor = "Transparent"
    // A configurer via AimingDefaultColor
    ReloadIconChronoForegroundColor = "Cyan"

    // Player Name
    PlayerNameTextStyle = "PlayerName"
    PlayerNameTextColor = "Fulda_Turquoise"
    PlayerNameTypefaceToken = "UIMainFont"

    PlayerNameTextSize = "20"

    PlayerNamePadding = TRTTILength4(Magnifiable = [1.0, 1.0, 1.0, 1.0])
)
//-------------------------------------------------------------------------------------
// nom & nb unités d'infanterie
private MultiSelectionCarriedUnitNameList is TCarriedUnitLabelDescriptor
(
    ElementName = "CarriedUnitNameList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    NameAndUnitCountDescription = ~/MultiSelectionCarriedNameAndUnitCountDescription

    HasBackground = false
    IsClippable = true

    BackgroundLocalRenderLayer = 2
    LocalRenderLayer = 3

    UniformDrawer = $/UserInterface/UIUniformDrawer
)
//-------------------------------------------------------------------------------------
private UpperLabelMultiSelection is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [100.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    ClipContent = true

    // Penser a modifier la taille en Pixel du LabelUnitBUCKComponent si on aggrandit ce composant !
    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/MultiSelectionUnitLabelUpperList
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/MultiSelectionUnitIcon
        ),
        BUCKListElementSpacer(Magnifiable = 1.0),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/MultiSelectionCarriedUnitNameList
        ),
    ]
)
//-------------------------------------------------------------------------------------
private UnitLabelUnitBUCKComponentDescriptorForMultiSelection is BUCKLocalLayerContainerDescriptor
(
    ElementName = "LocalLayerContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableOffset  = [0.0, 0.0]
        AlignementToFather = [0.5, 1.0]
        AlignementToAnchor = [0.5, 1.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContent
    NbLayersToLock = 6

    Components = [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [100.0, 62.0]
            )

            IsClippable = true

            Components =
            [
                BUCKSensibleAreaDescriptor
                (
                    ElementName = "BackgroundSensibleArea"

                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                    )

                    Components =
                    [
                        PanelRoundedCorner
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 1.0]
                            )

                            BackgroundLocalRenderLayer = 1
                            BorderLocalRenderLayer = 1
                            Radius = 3
                            BackgroundBlockColorToken = 'BoutonTemps_Background'
                            BorderLineColorToken = 'BoutonTemps_Line'
                        ),
                    ]
                ),
                ~/UpperLabelMultiSelection,
            ]
        )
    ]
)
//-------------------------------------------------------------------------------------
// L'etiquette dans le panel en bas
UISpecificUnitLabelViewForMultiSelectionDescriptor is TUISpecificInGameUnitLabelViewForMultiSelectionDescriptor
(
    MainComponentDescriptor = UnitLabelUnitBUCKComponentDescriptorForMultiSelection

    ExperienceTexturesResources = ~/ExperienceTexturesResources

    // Animation Concealed
    // Temps de durée d'un blink
    AnimConcealedBlinkDuration = 2.0
    // Animation Panicked
    // Temps de durée d'un blink
    AnimPanickedBlinkDuration = 1.5
    // Animation Shaken
    // Temps de pause entre les séries de blink (0 => pas de pause)
    AnimShakenPauseTime = 3.0
    // Temps de durée d'un blink
    AnimShakenBlinkDuration = 1.5
    // Nombre de blinks par série (-1 => infini)
    AnimShakenNbBlinks = 3
    // Alpha Minimum atteint pour les animation de "suppress"
    SuppressAnimAlphaMinimum = 20
    // Alpha Minimum atteint pour l'animation "cachée"
    ConcealedAnimAlphaMinimum = 20

    // Textures
    NormalBackgroundTexture = "CommonTexture_Label_Background"
    CarriedUnitIconUnknown  = "UseInGame_Transport_UNKNOW"
)