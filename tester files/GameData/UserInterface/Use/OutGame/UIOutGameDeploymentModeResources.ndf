

template GameModeDecorationComponent
[
    TextureToken : string = "",
    HintTitle : string = "",
    HintBody : string = "",
    HintExtended : string = "",
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1,1] )

    Components =
    [
        BUCKTextureDescriptor
        (
            ElementName = 'TextureGameMode'
            ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1,1] )
            TextureToken        = <TextureToken>
            TextureFrame   = TUIFramePropertyRTTI( RelativeWidthHeight = [1,1] )
            ClipTextureToComponent = true
        ),
        BUCKTextureDescriptor
        (
            ElementName = "ObservableGameIndicator"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [33,33]
                AlignementToFather = [1,1]
                AlignementToAnchor = [1,1]
            )
            TextureToken        = "OutgameTexture_GameRoomList_Observable"
            TextureFrame   = TUIFramePropertyRTTI( RelativeWidthHeight = [1,1] )
            ClipTextureToComponent = true

            Components =
            [
                BUCKSpecificHintableArea
                (
                    DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                    HintTitleToken = "GRL_OBST"
                    HintBodyToken = "GRL_OBSB"
                    HintExtendedToken = "GRL_OBSE"
                ),
            ]
        ),
        BUCKSpecificHintableArea
        (
            ElementName = 'HintGameMode'
            DicoToken = ~/LocalisationConstantes/dico_interface_outgame
            HintTitleToken = <HintTitle>
            HintBodyToken = <HintBody>
            HintExtendedToken = <HintExtended>
        ),
    ]
)

DeploymentModeResources is TUIOutGameDeploymentModeResources
(
    DeploymentModeInfos =
    [
        //EDeploymentMode/NotSpecified,
        TUIDeploymentModeResource
        (
            Name = 'NON_APP'
            HintBody = 'NON_APP'
            GameRoomListFilterName = ''
            GameRoomListIconComponent = nil
        ),

        //EDeploymentMode/Conquest,
        TUIDeploymentModeResource
        (
            Name = 'GMODE_CONQ'
            HintBody = 'HGM_CQST'
            GameRoomListIconComponent = GameModeDecorationComponent
            (
                TextureToken = "OutgameTexture_ConquestModeTexture"
                HintTitle = "GRL_CONQT"
                HintBody = "GRL_CONQB"
                HintExtended = "GRL_CONQE"
            )
            GameRoomListFilterName = 'GRL_CONQ'
        ),

        //EDeploymentMode/Breakthrough,
        TUIDeploymentModeResource
        (
            Name = 'GMODE_BREA'
            HintBody = 'HGM_BREA'
            GameRoomListIconComponent = GameModeDecorationComponent
            (
                TextureToken = "OutgameTexture_BreakthroughModeTexture"
                HintTitle = "GRL_ATTP"
                HintBody = "GRL_ATTPB"
                HintExtended = "GRL_ATTPE"
            )
            GameRoomListFilterName = 'GRL_BREA'
        ),
    ]
)
