MainMenuSoloFontSize is "28"
MainMenuMultiFontSize is "23"
MainMenuModCenterFontSize is "23"
MainMenuArmoryFontSize is "25"

//------------------------------------------------------------------------------
private UIWelcomeQuitButton is BUCKButtonDescriptor
(
    ElementName = "QuitButton"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [200.0, 60.0]
        AlignementToAnchor = [0.5, 1.0]
        AlignementToFather = [0.5, 1.0]
    )
    HasBackground = true
    BackgroundBlockColorToken = "HUBOutGameNavBar/ButtonOverlay"
    LeftClickSound = ~/SoundEvent_QuitGame

    Components =
    [
        BUCKTextDescriptor
        (
            ParagraphStyle = TParagraphStyle
            (
                VerticalAlignment = ~/UIText_VerticalCenter
                Alignment = ~/UIText_Center
            )
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
            TextColor = "ListeExcel/Cartouche"
            TextSize  = "28"
            TextStyle = "Default"
            TypefaceToken = "UIMainFont"
            TextDico = ~/LocalisationConstantes/dico_interface_outgame
            TextToken = "AB_QUIT"
        ),
    ]
)

//------------------------------------------------------------------------------
private HUBProfileAvatar is BUCKListDescriptor
(
    Axis = ~/ListAxis/Vertical
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [128.0, 0.0]
    )

    FirstMargin = TRTTILength(Magnifiable = 10.0)
    InterItemMargin = TRTTILength(Magnifiable = 0.0)
    LastMargin = TRTTILength(Magnifiable = 2.0)


    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKSpecificAvatarDescriptor
            (
                ElementName = "HUBAccueilAvatarTexture"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [105.0, 105.0]
                    AlignementToFather = [0.5,0.0]
                    AlignementToAnchor = [0.5,0.0]
                    //RelativeWidthHeight = [1.0, 0.0]
                )
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 15.0]
                    RelativeWidthHeight = [1.0, 0.0]
                )

                TextSize = "10"
                TextColor = "TexteProfile"
                TextToken = "HAP_Badge"
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TypefaceToken = "UIMainFont"

                TextStyle = "Default"
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/UserDefined
            )
        ),

    ]
)

//------------------------------------------------------------------------------
private HUBProfileLevelInformation is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 20.0]
        RelativeWidthHeight = [1.0, 0.0]
    )
    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToAnchor = [0.0, 0.5]
                AlignementToFather = [0.0, 0.5]
            )
            Axis = ~/ListAxis/Horizontal
            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
            Elements =
            [


                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "HUBAccueilProfileIdMin"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [64.0, 0.0]
                        )

                        TextSize = "20"
                        TextColor = "TexteProfileTitre"
                        TextToken = "HAP_Badge"
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TypefaceToken = "Liberator"

                        TextStyle = "Default"
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Right
                            VerticalAlignment = UIText_VerticalCenter
                        )
                        HorizontalFitStyle = ~/FitStyle/UserDefined
                        VerticalFitStyle = ~/FitStyle/FitToContent
                    )
                ),

                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKGaugeDescriptor
                    (
                        ElementName = "HUBAccueilProfileIdGauge"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [128.0, 10.0]
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        HasBackground = true
                        BackgroundBlockColorToken = "TexteProfileGris"

                        GaugeMax = 100
                        GaugeValueMinSize = TRTTILength( Pixel = 1.0 )

                        GaugeComponentNames = ["HUBAccueilProfileIdGaugeValue"]

                        Components =
                        [
                            BUCKGaugeValueDescriptor
                            (
                                ElementName = "HUBAccueilProfileIdGaugeValue"
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 1.0]
                                )

                                HasBackground = true
                                BackgroundBlockColorToken = "TexteProfileTitre"
                            ),
                        ]
                    )
                ),

                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "HUBAccueilProfileIdMax"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [64.0, 0.0]
                        )

                        TextSize = "20"
                        TextColor = "TexteProfileTitre"
                        TextToken = "HAP_Badge"
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TypefaceToken = "Liberator"

                        TextStyle = "Default"
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Center
                            VerticalAlignment = UIText_VerticalCenter
                        )
                        HorizontalFitStyle = ~/FitStyle/UserDefined
                        VerticalFitStyle = ~/FitStyle/FitToContent
                    )
                ),

            ]
        )
    ]
)

//------------------------------------------------------------------------------

private HUBProfileNameAndLevel is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [320.0, 0.0]
    )

    FirstMargin = TRTTILength(Magnifiable = 20.0)
    Axis = ~/ListAxis/Vertical

    Elements =
    [
        //nom du joueur
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "HUBAccueilProfileIdName"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                TextSize = "28"
                TextColor = "TexteProfileTitre"
                TextToken = "HAP_Badge"
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TypefaceToken = "Liberator"

                TextStyle = "Default"
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent

                TextFormatScript = nil
            )
        ),
        //texte déco
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                TextSize = "14"
                TextColor = "TexteProfileGris"
                TextToken = "HAP_name"
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TypefaceToken = "Liberator"

                TextStyle = "Default"
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent
            )
        ),
        BUCKListElementSpacer(Magnifiable = 5.0),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "HUBAccueilProfileIdLevel"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 20.0]
                    RelativeWidthHeight = [1.0, 0.0]
                )

                TextSize = "26"
                TextColor = "TexteProfileTitre"
                TextToken = "HAP_Level"
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TypefaceToken = "Liberator"

                TextStyle = "Default"
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/UserDefined
            )
        ),

        BUCKListElementDescriptor( ComponentDescriptor = HUBProfileLevelInformation ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                TextSize = "14"
                TextColor = "TexteProfileGris"
                TextToken = "HAP_Merit"
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TypefaceToken = "Liberator"

                TextStyle = "Default"
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent
            )
        ),

    ]
)

//------------------------------------------------------------------------------
private HUBProfileMainContainer is BUCKPerspectiveWarpOffscreenContainerDescriptor
(
    ElementName = "HUBAccueilProfileButtonContainer"
    DistortTextureDrawer = $/UserInterface/UIDistortTextureDrawer
    PointerEventsToAllow = ~/EAllowablePointerEventType/Move

    MagnifiableTopLeftOffset = [9.6, 0.9]
    MagnifiableTopRightOffset = [-18.5, -4.9]
    MagnifiableBottomLeftOffset = [0.0, 0.0]
    MagnifiableBottomRightOffset = [-1.3, -9.4]

    ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )


    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToFather = [0.0, 0.5]
                AlignementToAnchor = [0.0, 0.5]
                RelativeWidthHeight = [0.0, 1.0]
            )

            Axis = ~/ListAxis/Horizontal

            Elements =
            [
                BUCKListElementDescriptor( ComponentDescriptor = HUBProfileAvatar ),
                BUCKListElementDescriptor( ComponentDescriptor = HUBProfileNameAndLevel ),
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

template  BoutonMenuPrincipal
[
    TextColor : string,
    MagnifiableTopLeftOffset : float2 = [0.0, 0.0],
    MagnifiableTopRightOffset : float2 = [0.0, 0.0],
    MagnifiableBottomLeftOffset : float2 = [0.0, 0.0],
    MagnifiableBottomRightOffset : float2 = [0.0, 0.0],
    TextSize : string,
    TextToken : string,
] is BUCKPerspectiveWarpOffscreenContainerDescriptor
(
    DistortTextureDrawer = $/UserInterface/UIDistortTextureDrawer
    PointerEventsToAllow = ~/EAllowablePointerEventType/Move
    ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )
    MagnifiableTopLeftOffset = <MagnifiableTopLeftOffset>
    MagnifiableTopRightOffset = <MagnifiableTopRightOffset>
    MagnifiableBottomLeftOffset = <MagnifiableBottomLeftOffset>
    MagnifiableBottomRightOffset = <MagnifiableBottomRightOffset>

    Components =
    [
        BUCKTextDescriptor
        (

            ParagraphStyle = TParagraphStyle ( VerticalAlignment = ~/UIText_VerticalCenter Alignment = ~/UIText_Center )

            ComponentFrame = TUIFramePropertyRTTI ( AlignementToFather = [0,0.5] AlignementToAnchor = [0,0.5] )

            HorizontalFitStyle = ~/FitStyle/FitToContent
            VerticalFitStyle = ~/FitStyle/FitToContent

            TextColor = <TextColor>
            TextSize  = <TextSize>
            TextStyle = "Default"
            TypefaceToken = "Liberator"
            TextToken = <TextToken>
            TextDico = ~/LocalisationConstantes/dico_interface_outgame
        )
    ]
)

//------------------------------------------------------------------------------
private MainOutGameWelcomeDescriptor is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
    Components =
    [
        BUCKTextureDescriptor
        (
            ElementName = "HUBAccueilTextureBackground"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
            TextureFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
            TextureToken = "Outgame_backgroundOfflineHomeScreen"
        ),

        BUCKButtonDescriptor
        (
            ElementName = "HUBAccueilSoloButton"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [0.0, 270.0]
                MagnifiableWidthHeight = [372.0, 500.0]
                AlignementToFather = [1.0, 0.0]
                AlignementToAnchor = [1.0, 0.0]
            )
            LeftClickSound = SoundEvent_EnterSolo
            Components =
            [
                BUCKSpecificHintableArea
                (
                    HintTitleToken = 'hsolot'
                    HintBodyToken = 'hsolob'
                    DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                ),
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [1920,1080]
                        MagnifiableOffset = [-1920.0+372, -270.0]
                    )

                    TextureToken = 'Outgame_HL_solo'
                ),

                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableOffset = [-75.0, 65]
                        MagnifiableWidthHeight = [280.0, 30.0]
                        AlignementToFather = [1.0, 0.0]
                        AlignementToAnchor = [1.0, 0.0]
                    )
                    HasBackground =false
                    BackgroundBlockColorToken = 'Vert'
                    Components =
                    [
                        BoutonMenuPrincipal
                        (
                            TextColor = 'Blanc_Home_hidden'
                            MagnifiableTopLeftOffset = [0, 0]
                            MagnifiableTopRightOffset = [0, -38]
                            MagnifiableBottomLeftOffset = [0.0, 5.0]
                            MagnifiableBottomRightOffset = [0, -22]
                            TextSize = ~/MainMenuSoloFontSize
                            TextToken = "AB_SOLO"
                        ),
                    ]
                ),

            ]
        ),
        BUCKButtonDescriptor
        (
            ElementName = "HUBAccueilMultiButton"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [1100.0, 300.0]
                MagnifiableWidthHeight = [440.0, 450.0]
            )
            LeftClickSound = SoundEvent_EnterMulti
            Components =
            [
                BUCKSpecificHintableArea
                (
                    HintTitleToken = 'hmultit'
                    HintBodyToken = 'hmultib'
                    DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                ),
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [1920,1080]
                        MagnifiableOffset = [-1100, -300]
                    )
                    TextureToken = 'Outgame_HL_multi'


                ),
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableOffset = [62.0, 82.0]
                        MagnifiableWidthHeight = [280.0, 25.0]
                    )

                    Components =
                    [
                        BoutonMenuPrincipal
                        (
                            TextColor = 'Blanc_Home_hidden'
                            MagnifiableTopLeftOffset = [0, 0]
                            MagnifiableTopRightOffset = [0, -20-11]
                            MagnifiableBottomLeftOffset = [0.0, 0.0]
                            MagnifiableBottomRightOffset = [0, -18-9]
                            TextSize = ~/MainMenuMultiFontSize
                            TextToken = "AB_MULTI"
                        ),
                    ]
                ),
            ]
        ),
        BUCKButtonDescriptor
        (
            ElementName = "HUBAccueilBattleGroupButton"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [300.0, 250.0]
                MagnifiableWidthHeight = [360.0, 363.0]
            )
            LeftClickSound = SoundEvent_EnterArmory
            Components =
            [
                BUCKSpecificHintableArea
                (
                    HintTitleToken = 'harmt'
                    HintBodyToken = 'harmb'
                    DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                ),
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [1920,1080]
                        MagnifiableOffset = [-300.0, -250.0]
                    )
                    TextureToken = 'Outgame_HL_armory'

                ),
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableOffset = [83, 67]
                        MagnifiableWidthHeight = [225.0, 25.0]
                    )

                    Components =
                    [
                        BoutonMenuPrincipal
                        (
                            TextColor = 'Blanc_Home_hidden'
                            MagnifiableTopLeftOffset = [0, 0]
                            MagnifiableTopRightOffset = [0, 14.25]
                            MagnifiableBottomLeftOffset = [0.0, 0.0]
                            MagnifiableBottomRightOffset = [0, 13.5]
                            TextSize = ~/MainMenuArmoryFontSize
                            TextToken = "AB_ARMORY"
                        ),
                    ]
                )
            ]
        ),
        BUCKButtonDescriptor
        (
            ElementName = "HUBAccueilModCenterButton"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [710.0, 350.0]
                MagnifiableWidthHeight = [395.0, 352.0]
            )

            LeftClickSound = SoundEvent_EnterMods

            Components =
            [
                BUCKSpecificHintableArea
                (
                    HintTitleToken = 'hmodt'
                    HintBodyToken = 'hmodb'
                    DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                ),
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [1920,1080]
                        MagnifiableOffset = [-710.0, -350.0]
                    )
                    TextureToken = 'Outgame_HL_mods'
                ),
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableOffset = [104.0, 65.0]
                        MagnifiableWidthHeight = [150.0, 25.0]
                    )

                    Components =
                    [
                        BoutonMenuPrincipal
                        (
                            TextColor = 'Blanc_Home_hidden'
                            MagnifiableTopLeftOffset = [0, 1]
                            MagnifiableTopRightOffset = [0, 2]
                            MagnifiableBottomLeftOffset = [0.0, 1]
                            MagnifiableBottomRightOffset = [0, 0]
                            TextSize = ~/MainMenuModCenterFontSize
                            TextToken = "AB_MODCENT"
                        ),
                    ]
                ),
            ]
        ),
        BUCKButtonDescriptor
        (
            ElementName = "HUBAccueilOptionButton"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [-50, -100]
                AlignementToAnchor = [1,1]
                AlignementToFather = [1,1]
                MagnifiableWidthHeight = [550, 200]
            )
            LeftClickSound = SoundEvent_EnterOptions
            HasBackground =false
            BackgroundBlockColorToken = 'Orange'
            Components =
            [
                BUCKSpecificHintableArea
                (
                    HintTitleToken = 'hoptiont'
                    HintBodyToken = 'hoptionb'
                    DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                ),
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                    MagnifiableWidthHeight = [1920,1080]
                    MagnifiableOffset = [-1920+600,-1080+300]
                    )
                    TextureToken = 'Outgame_HL_options'
                )
            ]
        ),
        BUCKButtonDescriptor
        (
            ElementName = "HUBAccueilProfileButton"
            ComponentFrame = TUIFramePropertyRTTI
            (
                //RelativeOffset = [0.369, 0.751]
                AlignementToAnchor = [0.5,1]
                AlignementToFather = [0.5,1]
                MagnifiableOffset = [30,-140]
                MagnifiableWidthHeight = [568.0, 133.0]
            )
            LeftClickSound = SoundEvent_MainMenuOpenProfile
            HasBackground = false
            BackgroundBlockColorToken = "Orange"
            Components =
            [
                BUCKSpecificHintableArea
                (
                    HintTitleToken = 'hprofilet'
                    HintBodyToken = 'hprofileb'
                    DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                ),
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [1920,1080]
                        MagnifiableOffset = [0.5*(-1920+568) -30,-1080+273]
                    )
                    TextureToken = 'Outgame_HL_profile'
                ),
                HUBProfileMainContainer,
            ]
        ),
        BUCKContainerDescriptor
        (
            UniqueName = "LoginPanel"

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
        ),
        // effet par dessus
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [ 1.0, 1.0 ]
            )

            TextureFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
            TextureToken = "outgame_fond_test"
            TextureDrawer  = 'ColorMultiply_Additive'
        ),
        // affichage navigation
        // solo
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [-75.0, 335.0]
                MagnifiableWidthHeight = [280.0, 30.0]
                AlignementToFather = [1.0, 0.0]
                AlignementToAnchor = [1.0, 0.0]
            )

            Components =
            [
                BoutonMenuPrincipal
                (
                    TextColor = 'Blanc_Home'
                    MagnifiableTopLeftOffset = [0, 0]
                    MagnifiableTopRightOffset = [0, -38]
                    MagnifiableBottomLeftOffset = [0.0, 5.0]
                    MagnifiableBottomRightOffset = [0, -22]
                    TextSize = ~/MainMenuSoloFontSize
                    TextToken = "AB_SOLO"
                ),
            ]
        ),
        // multi
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [1162.0, 382.0]
                MagnifiableWidthHeight = [280.0, 25.0]
            )

            Components =
            [
                BoutonMenuPrincipal
                (
                    TextColor = 'Blanc_Home'
                    MagnifiableTopLeftOffset = [0, 0]
                    MagnifiableTopRightOffset = [0, -20-11]
                    MagnifiableBottomLeftOffset = [0.0, 0.0]
                    MagnifiableBottomRightOffset = [0, -18-9]
                    TextSize = ~/MainMenuMultiFontSize
                    TextToken = "AB_MULTI"
                ),
            ]
        ),
        // ModCenter
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [814.0, 415.0]
                MagnifiableWidthHeight = [150.0, 25.0]
            )

            Components =
            [
                BoutonMenuPrincipal
                (
                    TextColor = 'Blanc_Home'
                    MagnifiableTopLeftOffset = [0, 1]
                    MagnifiableTopRightOffset = [0, 2]
                    MagnifiableBottomLeftOffset = [0.0, 1]
                    MagnifiableBottomRightOffset = [0, 0]
                    TextSize = ~/MainMenuModCenterFontSize
                    TextToken = "AB_MODCENT"
                ),
            ]
        ),
        // Armory
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [383.0, 317.0]
                MagnifiableWidthHeight = [225.0, 25.0]
            )

            Components =
            [
                BoutonMenuPrincipal
                (
                    TextColor = 'Blanc_Home'
                    MagnifiableTopLeftOffset = [0, 0]
                    MagnifiableTopRightOffset = [0, 14.25]
                    MagnifiableBottomLeftOffset = [0.0, 0.0]
                    MagnifiableBottomRightOffset = [0, 13.5]
                    TextSize = ~/MainMenuArmoryFontSize
                    TextToken = "AB_ARMORY"
                ),
            ]
        )
    ]
)
//------------------------------------------------------------------------------

private UISpecificOutGameWelcomeDescriptor is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )
    Components =
    [
        ~/MainOutGameWelcomeDescriptor,
        ~/UIWelcomeQuitButton,
    ]
)

//------------------------------------------------------------------------------

UISpecificOutGameWelcomeViewDescriptor is TUISpecificOutGameWelcomeViewDescriptor
(
    MainComponentDescriptor = ~/UISpecificOutGameWelcomeDescriptor
    TextureNameBackgroundOnline = "Outgame_backgroundOfflineHomeScreen"
    TextureNameBackgroundOffline = "Outgame_backgroundOfflineHomeScreen"
    MainComponentContainerUniqueName = ""
)

