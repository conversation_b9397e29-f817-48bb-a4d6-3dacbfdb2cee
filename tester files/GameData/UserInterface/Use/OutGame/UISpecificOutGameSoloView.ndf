
//------------------------------------------------------------------------------

MainOutGameSoloDescriptor is BUCKContainerDescriptor
(
    ElementName = 'MainOutGameSoloDescriptor'

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            TextureFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
            TextureToken = "OutgameTexture_outgame_Solo"
        ),

        ~/ListeDesActionsSolo,

        BUCKContainerDescriptor
        (
            UniqueName = "SubMenuPanelViewDescriptor"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
        ),
        MainBackButtonContainer
        (
            ButtonDefaultToken = "BTN_BACK"
            BackMapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ESCAPE ) )
        ),
        // effet par dessus
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [ 1.0, 1.0 ]
            )
            TextureFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
            TextureToken = "outgame_fond_test"
            TextureDrawer  = 'ColorMultiply_Additive'
        ),
    ]
)

//-------------------------------------------------------------------------------------

ListeDesActionsSolo is BUCKListDescriptor
(
    ElementName = "ListeDesActionsSolo"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 221.0]
        MagnifiableOffset = [-52.0, 265.0]
    )

    Axis = ~/ListAxis/Horizontal

    FirstMargin = TRTTILength(Magnifiable = 200.0)
    InterItemMargin = TRTTILength(Magnifiable = 42.0)
    LastMargin = TRTTILength(Magnifiable = 200.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = ThumbnailSoloDescriptor
            (
                ElementName = "HUBSoloTutorialButton"
                TextToken = "SGP_TUTO"
                ForceEvents = true
                LeftClickSound = SoundEvent_EnterSoloTutorial
            )
        ),

        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = ThumbnailSoloDescriptor
            (
                ElementName = "HUBStrategicButton"
                TextToken = "SGP_CAMP"
                ForceEvents = true
                LeftClickSound = SoundEvent_EnterSoloCampaign
            )
        ),

        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = ThumbnailSoloDescriptor
            (
                ElementName = "HUBSkirmishButton"
                TextToken = "SGP_SKIR"
                ForceEvents = true
                LeftClickSound = SoundEvent_EnterSoloSkirmish
            )
        ),

        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = ThumbnailSoloDescriptor
            (
                ElementName = "HUBChallengeButton"
                TextToken = "OP_NAME"
                ForceEvents = true
                LeftClickSound = SoundEvent_EnterSoloChallenge
            )
        ),

        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = ThumbnailSoloDescriptor
            (
                ElementName = "HUBLoadFileButton"
                TextToken = "AB_LOADING"
                ForceEvents = true
                LeftClickSound = SoundEvent_EnterSoloLoad
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------
private ThumbnailSoloRadioButtonManager is TBUCKRadioButtonManager()

template ThumbnailSoloDescriptor
[
    TextToken : string,
    ElementName: string,
    DefaultToggleValue: boolean = false,
    ForceEvents: boolean = false,
    TextColor : string = "menu_titre_solo",
    TextSize : string = "28",
    DecalageTextureSurvol : float2 = [-15.0, 40.0],
    TextureColorToken : string = '',
    LeftClickSound : TSoundDescriptor = nil,
]
 is BUCKButtonDescriptor
(
    ElementName = <ElementName>

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        MagnifiableWidthHeight = [291.0, 0.0]
    )

    IsTogglable = true
    DefaultToggleValue = <DefaultToggleValue>
    RadioButtonManager = ~/ThumbnailSoloRadioButtonManager
    CannotDeselect = true
    ForceEvents = <ForceEvents>

    LeftClickSound = <LeftClickSound>

    Components =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [293.0, 38.0]
                AlignementToFather = [0.5, 0.0]
                AlignementToAnchor = [0.5, 0.0]
                MagnifiableOffset = [0.0, -10.0]
            )
            TextureFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
            TextureToken =  'Outgame_survol_menuSolo'
            TextureColorToken = <TextureColorToken>
        ),
        BUCKTextDescriptor
        (
            ParagraphStyle = TParagraphStyle
            (
                VerticalAlignment = ~/UIText_Bottom
                Alignment = ~/UIText_Center
            )
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
                MagnifiableWidthHeight = [0.0, 25.0]
            )
            HasBackground = false
            BackgroundBlockColorToken = "BlancLight"
            TextColor = <TextColor>
            TextSize  = <TextSize>
            TextStyle = "Default"
            TypefaceToken = "Liberator"
            TextToken = <TextToken>
            TextDico = ~/LocalisationConstantes/dico_interface_outgame

            TextPadding = TRTTILength4( Magnifiable = [0.0, 0.0, 0.0, 2.0] )
        ),
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [321.0, 211.0]
                MagnifiableOffset = <DecalageTextureSurvol>
            )
            TextureFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
            TextureToken =  'Outgame_survol_photoSolo'
        ),
        BUCKContainerDescriptor
        (
            ElementName = <ElementName> + "LockedOverlay"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [282,172]
                MagnifiableOffset = [2,47]
            )

            HasBackground = True
            BackgroundBlockColorToken = "SD2_Blanc184_70"

            Components =
            [
                BUCKTextDescriptor
                (
                    ParagraphStyle = TParagraphStyle
                    (
                        VerticalAlignment = ~/UIText_VerticalCenter
                        Alignment = ~/UIText_Center
                    )
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 0.0]
                        AlignementToAnchor = [0.0, 0.5]
                        AlignementToFather = [0.0, 0.5]
                    )

                    BigLineAction = ~/BigLineAction/MultiLine
                    VerticalFitStyle = ~/FitStyle/FitToContent

                    TextColor = "Blanc"
                    TextSize  = "40"
                    TextStyle = "Default"
                    TypefaceToken = "Liberator"
                    TextToken = ("AB_BLCK_EA")
                    TextDico = ~/LocalisationConstantes/dico_interface_outgame
                ),
            ]
        ),
    ]
)

//------------------------------------------------------------------------------

UISpecificOutGameSoloViewDescriptor is TUISpecificOutGameSoloViewDescriptor
(
    MainComponentDescriptor = ~/MainOutGameSoloDescriptor
    MainComponentContainerUniqueName = ""
)
