//-------------------------------------------------------------------------------------

template DebriefTeamStatsMultiListContent
[
]
is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 35.0]
    )

    Axis = ~/ListAxis/Horizontal
    HasBackground = true
    BackgroundBlockColorToken = "Menu/SaveLoadLine"

    Elements =
    [
        // Avatar
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsAvatarMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

                Components =
                [
                    BUCKSpecificAvatarDescriptor
                    (
                        ElementName = "StatsAvatar"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [37.0, 0.0]
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )

                        ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                        TextureFrame = TUIFramePropertyRTTI
                        (
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )
                    ),
                ]
            )
        ),
        // Emblem
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsEmblemMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

                Components =
                [
                    BUCKTextureDescriptor
                    (
                        ElementName = "StatsEmblem"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [37.0, 0.0]
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )

                        ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                        TextureFrame = TUIFramePropertyRTTI
                        (
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )

                        Components =
                        [
                            BUCKSpecificHintableArea
                            (
                                ElementName = "StatsEmblemHint"
                                DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                            )
                        ]
                    )
                ]
            )
        ),
        // Player
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.2
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsPlayersMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                Components =
                [
                    BUCKButtonDescriptor
                    (
                        ElementName = "StatsPlayersButton"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        Components =
                        [
                            BUCKSpecificTextWithHint
                            (
                                ElementName = "StatsPlayers"
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 1.0]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Left
                                    VerticalAlignment = UIText_VerticalCenter
                                    BigWordAction = ~/BigWordAction/BigWordCut
                                )

                                BigLineAction = ~/BigLineAction/CutByDots

                                TextStyle = "Default"
                                TypefaceToken = "Eurostyle_Medium"

                                TextDico = ~/LocalisationConstantes/dico_interface_outgame

                                TextColor = "GrisTexteHighlightable"
                                TextSize = TacticalEndgameScoreTextFontSize

                                TextPadding = TRTTILength4(Magnifiable = [20.0, 0.0, 0.0, 0.0])

                                HasAutoHint = true
                                AutoHintElementName = "StatsPlayersHint"
                            ) // BUCKTextDescriptor
                        ]
                    ) // BUCKButtonDescriptor
                ]
            )
        ),
        // Add friend
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsAddFriendMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

                Components =
                [
                    BUCKButtonDescriptor
                    (
                        ElementName = "StatsAddFriend"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [50.0, 0.0]
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )

                        Components =
                        [
                            BUCKTextureDescriptor
                            (
                                ElementName = "AddFriendTexture"
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 1.0]
                                    MagnifiableWidthHeight = [50.0, 0]
                                    AlignementToAnchor = [0.5, 0.5]
                                    AlignementToFather = [0.5, 0.5]
                                )
                                ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio

                                TextureFrame = TUIFramePropertyRTTI
                                (
                                    AlignementToAnchor = [0.5, 0.5]
                                    AlignementToFather = [0.5, 0.5]
                                )

                                TextureToken = "Endgame_postit"
                            ),
                            BUCKTextDescriptor
                            (
                                ElementName = "AddFriendText"
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 1.0]
                                    MagnifiableWidthHeight = [30.0, 0.0]
                                    AlignementToAnchor = [0.5, 0.5]
                                    AlignementToFather = [0.5, 0.5]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                )

                                BigLineAction = ~/BigLineAction/MultiLine

                                TextStyle = "Default"
                                TypefaceToken = TacticalEndgamePostItTextFont
                                TextToken = "OC_HTADD"
                                TextDico = ~/LocalisationConstantes/dico_interface_outgame

                                TextColor = "GrisTexteHighlightable"
                                TextSize = TacticalEndgamePostItTextFontSize
                            )
                        ]
                    ),
                ]
            )
        ),
        // GameModePoints Best
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.02
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsGameModePointsMultiBestList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

                Components =
                [

                    Icone_BestScore(ElementName = "GameModePointsBest")
                ]
            )
        ),
        // Game mode Points
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.1
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsGameModePointsMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ElementName = "StatsGameModePoints"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_VerticalCenter
                        )

                        BigLineAction = ~/BigLineAction/CutByDots

                        TextStyle = "Default"
                        TypefaceToken = "Eurostyle"

                        TextDico = ~/LocalisationConstantes/dico_interface_outgame

                        TextColor = "GrisTexte"
                        TextSize = TacticalEndgameScoreTextFontSize
                    )
                ]
            )
        ),
        // Kills Best
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.02
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsKillsMultiBestList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

                Components =
                [
                    Icone_BestScore(ElementName = "KillsBest")
                ]
            )
        ),
        // Kills
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.1
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsKillsMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ElementName = "StatsKills"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_VerticalCenter
                        )

                        BigLineAction = ~/BigLineAction/CutByDots

                        TextStyle = "Default"
                        TypefaceToken = "Eurostyle"

                        TextDico = ~/LocalisationConstantes/dico_interface_outgame

                        TextColor = "GrisTexte"
                        TextSize = TacticalEndgameScoreTextFontSize
                    )
                ]
            )
        ),
        // Losses Best
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.02
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsLossesMultiBestList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

                Components =
                [
                    Icone_BestScore(ElementName = "LossesBest")
                ]
            )
        ),
        // Losses
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.1
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsLossesMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ElementName = "StatsLosses"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_VerticalCenter
                        )

                        BigLineAction = ~/BigLineAction/CutByDots

                        TextStyle = "Default"
                        TypefaceToken = "Eurostyle"

                        TextDico = ~/LocalisationConstantes/dico_interface_outgame

                        TextColor = "GrisTexte"
                        TextSize = TacticalEndgameScoreTextFontSize
                    )
                ]
            )
        ),
        // Kill Ratio Best
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.02
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsKillRatioMultiBestList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

                Components =
                [
                    Icone_BestScore(ElementName = "KillRatioBest")
                ]
            )
        ),
        // Kill Ratio
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.1
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "StatsKillRatioMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ElementName = "StatsKillRatio"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_VerticalCenter
                        )

                        BigLineAction = ~/BigLineAction/CutByDots

                        TextStyle = "Default"
                        TypefaceToken = "Eurostyle"

                        TextDico = ~/LocalisationConstantes/dico_interface_outgame

                        TextColor = "GrisTexte"
                        TextSize = TacticalEndgameScoreTextFontSize
                    )
                ]
            )
        ),
        // HiddenSortingCriterion
        // Utilisé par le cpp pour trier les joueur avec la formule : nbKills - nbLosses + nb_points_de_commandement (FUL-1493)
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "HiddenSortingCriterion"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 0.0]
                )

                Components =
                [
                    BUCKContainerDescriptor
                    (
                        ElementName = "DummyForHiddenSortingCriterion"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [0.0, 0.0]
                        )
                    )
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------
Icone_BestScore_Size is 30

template Icone_BestScore
[

ElementName : string

]

is BUCKTextureDescriptor
(
    ElementName = <ElementName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        // RelativeWidthHeight = [0.0, 1.0]
        MagnifiableWidthHeight = [Icone_BestScore_Size, Icone_BestScore_Size]
        AlignementToAnchor = [0.5, 0.5]
        AlignementToFather = [0.5, 0.5]
    )

    ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
    TextureFrame = TUIFramePropertyRTTI
    (
        AlignementToAnchor = [0.5, 0.5]
        AlignementToFather = [0.5, 0.5]
    )

    TextureToken = 'Outgame_BestScore'
    // TextureColorToken = "Noir"
    TextureColorToken = "GrisTexteHighlightable"


    // Components =
    // [
    //     BUCKSpecificHintableArea
    //     (
    //         ElementName = "LossesBestHint"
    //         DicoToken = ~/LocalisationConstantes/dico_interface_outgame
    //     ),
    // ]
)
//-------------------------------------------------------------------------------------

template DebriefTeamStatsContainer
[
]
is BUCKContainerDescriptor
(
    ElementName = "TeamStatsContainer"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    HasBackground = true
    BackgroundBlockColorToken = "DivisionBrief/ButtonOverlay"

    HidePointerEvents = true

    Components =
    [
        DebriefTeamStatsMultiListContent(),
    ]
)

//-------------------------------------------------------------------------------------

template DebriefTeamStatsMultiListTitle
[
]
is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 40.0]
    )

    //HasBackground = true
    BackgroundBlockColorToken = "ListeExcel/Cartouche"

    Axis = ~/ListAxis/Horizontal

    Elements =
    [
        // Avatar
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsAvatarMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [37.0, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false
                HidePointerEvents = true
                Components = []
            )
        ),
        // Emblem
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsEmblemMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [37.0, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false
                HidePointerEvents = true
                Components = []
            )
        ),
        // Name
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.2
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsPlayersMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                SortingType = ~/MultiListSorting/String
                ShowCheckbox = false
                HidePointerEvents = true
                Components =
                [
                    BUCKTextDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = ~/UIText_Left
                            VerticalAlignment = ~/UIText_VerticalCenter
                            BigWordAction = ~/BigWordAction/BigWordNewLine
                        )
                        TextStyle = 'Default'
                        TypefaceToken = TacticalEndgameTitleFont
                        TextSize = TacticalEndgameTitleFontSize
                        TextColor = 'Transparent'
                        TextToken = 'EG_MPLAY'
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame

                        //  TextPadding = TRTTILength4(Magnifiable = [20.0, 0.0, 0.0, 0.0])
                    ),
                ]
            )
        ),
        // Add Friend
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsAddFriendMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [50.0, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false

                HidePointerEvents = true
                Components = []
            )
        ),
        // Game mode points Best
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.02
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsGameModePointsMultiBestList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [Icone_BestScore_Size, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false

                HidePointerEvents = true
                Components = []
            )
        ),
        // Game mode points
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.1
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsGameModePointsMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                HidePointerEvents = true
                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false
                Components =
                [
                    BUCKSpecificTextWithHint
                    (
                        ElementName = 'StatsGameModePointsTitleText'

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            MagnifiableOffset = [0.0, 0.0]
                        )
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = ~/UIText_Left
                            VerticalAlignment = ~/UIText_VerticalCenter
                            BigWordAction = ~/BigWordAction/BigWordNewLine
                        )
                        TextStyle = 'Default'
                        TypefaceToken = TacticalEndgameTitleFont
                        TextSize = TacticalEndgameTitleFontSize
                        TextColor = 'GrisFoncePapier'
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        HasAutoHint = true
                        AutoHintElementName = "GameModePointTitleAutoHint"
                    ),
                ]
            )
        ),
        // Kills Best
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.02
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsKillsMultiBestList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [Icone_BestScore_Size, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false

                HidePointerEvents = true
                Components = []
            )
        ),
        // Kills
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.1
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsKillsMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                HidePointerEvents = true

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false
                Components =
                [
                    BUCKSpecificTextWithHint
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            MagnifiableOffset = [0.0, 0.0]
                        )
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = ~/UIText_Left
                            VerticalAlignment = ~/UIText_VerticalCenter
                            BigWordAction = ~/BigWordAction/BigWordNewLine
                        )
                        TextStyle = 'Default'
                        TypefaceToken = TacticalEndgameTitleFont
                        TextSize = TacticalEndgameTitleFontSize
                        TextColor = 'GrisFoncePapier'
                        TextToken = 'EG_KILLS'
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        HasAutoHint = true
                        AutoHintElementName = "KillsTitleAutoHint"
                    ),
                ]
            )
        ),
        // Losses Best
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.02
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsLossesMultiBestList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [Icone_BestScore_Size, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false

                HidePointerEvents = true
                Components = []
            )
        ),
        // Losses
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.1
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsLossesMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                HidePointerEvents = true
                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false
                Components =
                [
                    BUCKSpecificTextWithHint
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            MagnifiableOffset = [0.0, 0.0]
                        )
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = ~/UIText_Left
                            VerticalAlignment = ~/UIText_VerticalCenter
                            BigWordAction = ~/BigWordAction/BigWordNewLine
                        )
                        TextStyle = 'Default'
                        TypefaceToken = TacticalEndgameTitleFont
                        TextSize = TacticalEndgameTitleFontSize
                        TextColor = 'GrisFoncePapier'
                        TextToken = 'EG_LOSSES'
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        HasAutoHint = true
                        AutoHintElementName = "LossesTitleAutoHint"
                    ),
                ]
            )
        ),
        // Kill ratio Best
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.02
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsKillRatioMultiBestList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [Icone_BestScore_Size, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false

                HidePointerEvents = true
                Components = []
            )
        ),
        // Kill ratio
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.1
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'StatsKillRatioMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )

                HidePointerEvents = true
                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false
                Components =
                [
                    BUCKSpecificTextWithHint
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            MagnifiableOffset = [0.0, 0.0]
                        )
                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = ~/UIText_Left
                            VerticalAlignment = ~/UIText_VerticalCenter
                            BigWordAction = ~/BigWordAction/BigWordNewLine
                        )
                        TextStyle = 'Default'
                        TypefaceToken = TacticalEndgameTitleFont
                        TextSize = TacticalEndgameTitleFontSize
                        TextColor = 'GrisFoncePapier'
                        TextToken = 'EG_UPRR'
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        HasAutoHint = true
                        AutoHintElementName = "KillRatioTitleAutoHint"
                    ),
                ]
            )
        ),
        // HiddenSortingCriterion
        // Utilisé par le cpp pour trier les joueur avec la formule : nbKills - nbLosses + nb_points_de_commandement (FUL-1493)
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = "HiddenSortingCriterion"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                Components =
                [
                    BUCKContainerDescriptor
                    (
                        ElementName = "DummyForHiddenSortingCriterion"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [0.0, 0.0]
                        )
                    )
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

template DebriefTeamStatsMultiList
[
]
is BUCKMultiListDescriptor
(
    ElementName = "DebriefTeamStatsMultiList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    ColumnNames =
    [
        "StatsAvatarMultiList",
        "StatsEmblemMultiList",
        "StatsPlayersMultiList",
        "StatsAddFriendMultiList",
        "StatsGameModePointsMultiBestList",
        "StatsKillsMultiBestList",
        "StatsLossesMultiBestList",
        "StatsKillRatioMultiBestList",
    ]

    SortableColumnNames =
    [
        "HiddenSortingCriterion",
    ]

    RackName = "TeamStatsRack"

    TitleDescriptor = DebriefTeamStatsMultiListTitle()
    ContentDescriptor = BUCKRackDescriptor
    (
        ElementName = "TeamStatsRack"

        ComponentFrame = TUIFramePropertyRTTI
        (
            RelativeWidthHeight = [1.0, 0.0]
        )

        // FirstMargin = TRTTILength(Magnifiable = 5.0)
        InterItemMargin = TRTTILength(Magnifiable = 2.0)
        LastMargin = TRTTILength(Magnifiable = 20.0)

        BladeDescriptor = DebriefTeamStatsContainer()
    )
)

//-------------------------------------------------------------------------------------
