EscapeMenuSpaceBetweenButtons is 12.0
EscapeMenuButtonWidth is 225.0

// -------------------------------------------------------------------------------------------------

private <PERSON>P<PERSON>lCont<PERSON>ue<PERSON>utton is ConfirmButton
(
    ElementName = "ContinueButton"
    TextToken = 'AB_CONTINU'

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "OES_CNTINT"
        HintBodyToken = "OES_CNTINB"
        HintExtendedToken = "OES_CNTINE"
    )

    ButtonMagnifiableWidthHeight = [EscapeMenuButtonWidth, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = 'TextStyleEcranMoniteur'
    TextColorToken = 'loginBlanc'
    BorderLineColorToken = 'VertLogin'
    HasBackground = true
    BackgroundBlockColorToken = 'loginBoutonBackground_vert'
    TextSizeToken = '24'
    BorderThicknessToken = '3'

    Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_F10 ) )
)

// -------------------------------------------------------------------------------------------------

private EscapePanelRestartButton is ConfirmButton
(
    ElementName = "RestartButton"
    TextToken = 'AB_RESTART'

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "OES_RSTRTT"
        HintBodyToken = "OES_RSTRTB"
        HintExtendedToken = "OES_RSTRTE"
    )

    ButtonMagnifiableWidthHeight = [EscapeMenuButtonWidth, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = 'TextStyleEcranMoniteur'
    TextColorToken = 'loginBlanc'
    BorderLineColorToken = 'VertLogin'
    HasBackground = true
    BackgroundBlockColorToken = 'loginBoutonBackground_vert'
    TextSizeToken = '24'
    BorderThicknessToken = '3'
)

// -------------------------------------------------------------------------------------------------

private EscapePanelHelpButton is ConfirmButton
(
    ElementName = "HelpButton"
    TextToken = 'AB_HELP'

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "OES_HELPT"
        HintBodyToken = "OES_HELPB"
        HintExtendedToken = "OES_HELPE"
    )

    ButtonMagnifiableWidthHeight = [EscapeMenuButtonWidth, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = 'TextStyleEcranMoniteur'
    TextColorToken = 'loginBlanc'
    BorderLineColorToken = 'VertLogin'
    HasBackground = true
    BackgroundBlockColorToken = 'loginBoutonBackground_vert'
    TextSizeToken = '24'
    BorderThicknessToken = '3'
)

// -------------------------------------------------------------------------------------------------

private EscapePanelSaveButton is ConfirmButton
(
    ElementName = "SaveButton"
    TextToken = 'AB_SAVEGAM'

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "OES_SAVET"
        HintBodyToken = "OES_SAVEB"
        HintExtendedToken = "OES_SAVEE"
    )

    ButtonMagnifiableWidthHeight = [EscapeMenuButtonWidth, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = 'TextStyleEcranMoniteur'
    TextColorToken = 'loginBlanc'
    BorderLineColorToken = 'VertLogin'
    HasBackground = true
    BackgroundBlockColorToken = 'loginBoutonBackground_vert'
    TextSizeToken = '24'
    BorderThicknessToken = '3'

    Components = [
        BUCKTextDescriptor
        (
            ElementName = "SaveDisabledText"

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
                AlignementToFather = [1.0, 0.5]
                AlignementToAnchor = [0.0, 0.5]
                MagnifiableOffset = [10.0, 0.0]
            )

            ParagraphStyle = ~/paragraphStyleTextCenter
            TextStyle = "Default"
            TypefaceToken = "Liberator"

            HorizontalFitStyle = ~/FitStyle/FitToContent

            TextToken = "AB_SAVEDIS"
            TextDico = ~/LocalisationConstantes/dico_interface_outgame

            TextColor = "Noir"
            TextSize = "BoutonURL"
        )
    ]
)

// -------------------------------------------------------------------------------------------------

private EscapePanelLoadButton is ConfirmButton
(
    ElementName = "LoadButton"
    TextToken = 'AB_LOADGAM'

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "OES_LOADT"
        HintBodyToken = "OES_LOADB"
        HintExtendedToken = "OES_LOADE"
    )

    ButtonMagnifiableWidthHeight = [EscapeMenuButtonWidth, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = 'TextStyleEcranMoniteur'
    TextColorToken = 'loginBlanc'
    BorderLineColorToken = 'VertLogin'
    HasBackground = true
    BackgroundBlockColorToken = 'loginBoutonBackground_vert'
    TextSizeToken = '24'
    BorderThicknessToken = '3'
)

// -------------------------------------------------------------------------------------------------

private EscapePanelOptionsButton is ConfirmButton
(
    ElementName = "OptionsButton"
    TextToken = 'AB_OPTIONS'

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "OES_OPTNST"
        HintBodyToken = "OES_OPTNSB"
        HintExtendedToken = "OES_OPTNSE"
    )

    ButtonMagnifiableWidthHeight = [EscapeMenuButtonWidth, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = 'TextStyleEcranMoniteur'
    TextColorToken = 'loginBlanc'
    BorderLineColorToken = 'VertLogin'
    HasBackground = true
    BackgroundBlockColorToken = 'loginBoutonBackground_vert'
    TextSizeToken = '24'
    BorderThicknessToken = '3'
)

// -------------------------------------------------------------------------------------------------

private EscapePanelTutorialGuideButton is ConfirmButton
(
    ElementName = "TutorialGuideButton"
    TextToken = 'AB_GUIDE'

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        ElementName = "TutorialGuideButtonHintableAreaElement"
        HintTitleToken = "OES_GUIDT"
        HintBodyToken = "OES_GUIDB"
        HintExtendedToken = "OES_GUIDE"
    )

    ButtonMagnifiableWidthHeight = [EscapeMenuButtonWidth, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = 'TextStyleEcranMoniteur'
    TextColorToken = 'loginBlanc'
    BorderLineColorToken = 'VertLogin'
    HasBackground = true
    BackgroundBlockColorToken = 'loginBoutonBackground_vert'
    TextSizeToken = '24'
    BorderThicknessToken = '3'
)

// -------------------------------------------------------------------------------------------------

private EscapeMenuGameManualButton is ConfirmButton
(
    ElementName = "EscapeMenuGameManualButton"
    TextToken = 'AB_GUIDE'

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        ElementName = "TutorialGuideButtonHintableAreaElement"
        HintTitleToken = "OES_GUIDT"
        HintBodyToken = "OES_GUIDB"
        HintExtendedToken = "OES_GUIDE"
    )

    ButtonMagnifiableWidthHeight = [EscapeMenuButtonWidth, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = 'TextStyleEcranMoniteur'
    TextColorToken = 'loginBlanc'
    BorderLineColorToken = 'Vert2Login'
    HasBackground = true
    BackgroundBlockColorToken = 'loginBoutonBackground_vert2'
    TextSizeToken = '24'
    BorderThicknessToken = '3'
)

// -------------------------------------------------------------------------------------------------

private EscapePanelSurrenderOrQuitButton is ConfirmButton
(
    ElementName = "SurrenderOrQuitButton"
    TextToken = 'AB_QUITGAM'

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        ElementName = "SurrenderOrQuitButtonHintableAreaElement"
        HintTitleToken = "OES_EXITT"
        HintBodyToken = "OES_EXITB"
        HintExtendedToken = "OES_EXITE"
    )

    ButtonAlignementToFather = [0.5, 0.0]
    ButtonAlignementToAnchor = [0.5, 0.0]
    ButtonMagnifiableWidthHeight = [EscapeMenuButtonWidth, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = 'TextStyleEcranMoniteur_cyan'
    TextColorToken = 'loginBlanc'
    BorderLineColorToken = 'Cyan'
    HasBackground = true
    BackgroundBlockColorToken = 'loginBoutonBackground_cyan'
    TextSizeToken = '24'
    BorderThicknessToken = '3'
)

// -------------------------------------------------------------------------------------------------

private MagnifiableEscapeMenuWidth is 400.0

fullEscapeMenu is BUCKContainerDescriptor
(
    ElementName = "fullEscapeMenu"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        ~/EscapeMenuDescriptor,
        ~/AnimatedGameManualContainer,
    ]
)

// -------------------------------------------------------------------------------------------------

private EscapeMenuDescriptor is BUCKListDescriptor
(
    ElementName = "EscapeMenuMainList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [ 0.5, 0.5 ]
        AlignementToAnchor = [ 0.5, 0.5 ]
    )

    Axis = ~/ListAxis/Vertical

    InterItemMargin = TRTTILength( Magnifiable = ~/EscapeMenuSpaceBetweenButtons )
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
    GridAlign = false

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = EscapeMenuGameManualButton // This button is the only one we are 100% sure it would be here. The other buttons are added in the c++ code.
        ),
    ]

    BackgroundComponents =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [904.0, 732.0]
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]
            )
            TextureToken = 'Outgame_modale_fond'
            TextureDrawer = 'MonochromeMonitorEffect'
        )
    ]

    ForegroundComponents =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [904.0, 732.0]
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]
                )
            TextureToken = 'Outgame_modale_foreground'
            TextureDrawer = 'ColorMultiply_Additive'
        ),
    ]
)

// -------------------------------------------------------------------------------------------------

AnimatedGameManualContainer is BUCKTranslationAnimatedContainerDescriptor
(
    ElementName = "AnimatedGameManualContainer"
    ButtonNameForTrigger = "EscapeMenuGameManualButton"

    FramePropertyBeforeAnimation = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.0, 1.0]
        AlignementToFather = [0.0, 0.0]
        MagnifiableOffset = [-2000.0, 0.0]
    )

    AnimationTotalDuration = 0.20
    FramePropertyAfterAnimation = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.0, 1.0]
        AlignementToFather = [0.0, 1.0]
        MagnifiableOffset = [0.0, 0.0]
    )

    Components =
    [
        ~/GameManualContainer
    ]
)

// -------------------------------------------------------------------------------------------------

EscapePanelResource is TUISpecificOutGameEscapePanelResources
(
    EscapePanelMenu = ~/fullEscapeMenu

    EscapePanelContinueButton = ~/EscapePanelContinueButton
    EscapePanelRestartButton = ~/EscapePanelRestartButton
    EscapePanelHelpButton = ~/EscapePanelHelpButton
    EscapePanelSaveButton = ~/EscapePanelSaveButton
    EscapePanelLoadButton = ~/EscapePanelLoadButton
    EscapePanelOptionsButton = ~/EscapePanelOptionsButton
    EscapePanelTutorialGuideButton = ~/EscapePanelTutorialGuideButton
    EscapePanelSurrenderOrQuitButton = ~/EscapePanelSurrenderOrQuitButton
)
